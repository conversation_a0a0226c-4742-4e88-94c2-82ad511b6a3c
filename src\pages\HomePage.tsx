// import React, { useState, useEffect, useCallback, useRef } from "react";
// import { Container, Box, Paper, Typography } from "@mui/material";
// import PDFViewer from "../components/PDFViewer";
// import AudioControls from "../components/AudioControls";
// import Header from "../components/Header";
// import Footer from "../components/Footer";
// import { usePDFDocument } from "../hooks/usePDFDocument";
// import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
// import type { TextItem } from "../types";
// import { extractTextFromPage } from "../utils/pdfHelpers";
// import { PageTitle } from "../components/page-title-underline";
// import { Mail, Phone, MapPin, Clock } from "lucide-react";

// const HomePage: React.FC = () => {
//   const {
//     documentInfo,
//     currentPageText,
//     highlights,
//     loadDocument,
//     setCurrentPage,
//     addHighlight,
//     clearHighlights,
//     isLoading,
//   } = usePDFDocument();

//   const {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions,
//     speakFromPosition,
//   } = useSpeechSynthesis();

//   const [currentHighlightedText, setCurrentHighlightedText] = useState("");
//   const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(
//     null
//   );
//   const [currentHighlightedWord, setCurrentHighlightedWord] =
//     useState<TextItem | null>(null);
//   const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
//   const [isAutoReading, setIsAutoReading] = useState(false);
//   const [isStoppedAndReady, setIsStoppedAndReady] = useState(false);
//   const [readWords, setReadWords] = useState<Set<string>>(new Set());
//   const [hoveredReadingPosition, setHoveredReadingPosition] = useState<{
//     text: string;
//     startIndex: number;
//     type: "word" | "sentence" | "paragraph";
//     textItem?: TextItem;
//   } | null>(null);
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     loadDocument("/src/assets/sample.pdf");
//   }, [loadDocument]);

//   const getPageTextAndItems = useCallback(
//     async (pageNumber: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc)
//         return { text: "", textItems: [] };

//       // Read individual pages sequentially to ensure no pages are skipped
//       const { fullText, textItems: pageTextItems } = await extractTextFromPage(
//         documentInfo.pdfDoc,
//         pageNumber
//       );

//       return { text: fullText, textItems: pageTextItems };
//     },
//     [documentInfo]
//   );

//   const handleTextSelection = useCallback(
//     async (text: string, pageIndex: number) => {
//       if (!text || !documentInfo || !documentInfo.pdfDoc) return;

//       // Only allow text selection when audio is stopped or paused
//       const isClickable = !speaking || paused;
//       if (!isClickable) return;

//       // Stop current reading if any
//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       // Get the full text for the current page
//       const { text: fullText, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );

//       // Find the starting position of the selected text in the full text
//       const selectedTextIndex = fullText.indexOf(text);
//       if (selectedTextIndex === -1) {
//         // Fallback: just speak the selected text directly
//         setCurrentHighlightedText(text);
//         addHighlight({ pageIndex, text });
//         speak(
//           text,
//           [],
//           pageIndex,
//           (event) => {
//             if (
//               event.charIndex !== undefined &&
//               event.charLength !== undefined
//             ) {
//               const charIndex = event.charIndex;
//               const currentTextItem = textItems.find(
//                 (item) =>
//                   charIndex >= item.startIndex && charIndex < item.endIndex
//               );
//               if (currentTextItem) {
//                 setCurrentHighlightedWord(currentTextItem);
//               }
//             }
//           },
//           () => {
//             setCurrentHighlightedWord(null);
//           }
//         );
//         return;
//       }

//       // Start reading from the selected text position
//       setCurrentHighlightedText(text);
//       addHighlight({ pageIndex, text });
//       speakFromPosition(
//         fullText,
//         selectedTextIndex,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [
//       documentInfo,
//       speaking,
//       paused,
//       stop,
//       getPageTextAndItems,
//       speakFromPosition,
//       addHighlight,
//       speak,
//     ]
//   );

//   const handleWordClick = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       // Stop current reading
//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       // Get the full text for the current page
//       const { text, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );

//       // Find the position of the clicked word in the full text
//       const wordStartIndex = textItem.startIndex;

//       // Start reading from the clicked word
//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceClick = useCallback(
//     async (sentence: string, startIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       // Stop current reading
//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       // Get the full text for the current page
//       const { text, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );

//       // Start reading from the sentence
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );
//   const handleParagraphClick = useCallback(
//     async (paragraph: string, startIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       // Stop current reading
//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       // Get the full text for the current page
//       const { text, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );

//       // Start reading from the paragraph
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleWordHover = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       // Always track hovered position for potential click-to-read
//       if (textItem) {
//         setHoveredReadingPosition({
//           text: word,
//           startIndex: textItem.startIndex,
//           type: "word",
//           textItem: textItem,
//         });
//       } else {
//         setHoveredReadingPosition(null);
//       }

//       // Allow hover reading when audio is stopped or paused
//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       // Get the full text for the current page
//       const { text, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );

//       // Find the position of the hovered word in the full text
//       const wordStartIndex = textItem.startIndex;

//       // Start reading from the hovered word
//       setIsStoppedAndReady(false); // Exit stopped-and-ready mode
//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           // Re-enter stopped-and-ready mode only if we're still not speaking
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceHover = useCallback(
//     async (sentence: string, startIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       // Allow hover reading when audio is stopped or paused
//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       // Get the full text for the current page
//       const { text, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );

//       // Start reading from the sentence
//       setIsStoppedAndReady(false); // Exit stopped-and-ready mode
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           // Re-enter stopped-and-ready mode only if we're still not speaking
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleParagraphHover = useCallback(
//     async (paragraph: string, startIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       // Allow hover reading when audio is stopped or paused
//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       // Get the full text for the current page
//       const { text, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );

//       // Start reading from the paragraph
//       setIsStoppedAndReady(false); // Exit stopped-and-ready mode
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           // Re-enter stopped-and-ready mode only if we're still not speaking
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleReadingComplete = useCallback(async () => {
//     if (!documentInfo || !isAutoReading) return;

//     const currentPage = documentInfo.currentPage;
//     const nextPage = currentPage + 1;

//     if (nextPage > documentInfo.totalPages) {
//       // Reached the end of the document
//       setIsAutoReading(false);
//       stop();
//       clearHighlights();
//       setCurrentHighlight(null);
//       setCurrentHighlightedWord(null);
//       setReadWords(new Set());
//       setIsStoppedAndReady(true);
//     } else {
//       // Move to the next page
//       stop();
//       setReadWords(new Set()); // Clear read words for new page
//       await setCurrentPage(nextPage);
//       setTimeout(() => {
//         if (isAutoReading) {
//           setIsAutoReading(true);
//         }
//       }, 500);
//     }
//   }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights]);

//   useEffect(() => {
//     if (
//       !isAutoReading ||
//       !documentInfo ||
//       speaking ||
//       isLoading ||
//       !documentInfo.pdfDoc
//     )
//       return;

//     const readingTimeout = setTimeout(async () => {
//       const { text, textItems } = await getPageTextAndItems(
//         documentInfo.currentPage
//       );
//       setSpreadTextItems(textItems);
//       setCurrentHighlightedText(text);
//       speak(
//         text,
//         [],
//         documentInfo.currentPage - 1,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const textItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (textItem) {
//               const word = text
//                 .substring(charIndex, charIndex + event.charLength)
//                 .trim(); // Trim only the extracted word, not the source text

//               // Calculate precise word boundaries within the text item
//               const wordStart = Math.max(charIndex, textItem.startIndex);
//               const wordEnd = Math.min(
//                 charIndex + event.charLength,
//                 textItem.endIndex
//               );

//               // Ensure we don't have negative or zero lengths
//               const textItemLength = Math.max(
//                 textItem.endIndex - textItem.startIndex,
//                 1
//               );
//               const wordLength = Math.max(wordEnd - wordStart, 1);

//               // Calculate the precise position and width of the spoken word
//               const wordProgress =
//                 (wordStart - textItem.startIndex) / textItemLength;
//               const wordLengthRatio = wordLength / textItemLength;

//               // Ensure progress values are within valid bounds
//               const clampedProgress = Math.max(0, Math.min(1, wordProgress));
//               const clampedLengthRatio = Math.max(
//                 0.1,
//                 Math.min(1, wordLengthRatio)
//               );

//               const preciseX =
//                 textItem.coordinates.x +
//                 textItem.coordinates.width * clampedProgress;
//               const preciseWidth = Math.max(
//                 textItem.coordinates.width * clampedLengthRatio,
//                 15
//               ); // Minimum width for visibility

//               const highlightItem = {
//                 ...textItem,
//                 text: word,
//                 startIndex: wordStart,
//                 endIndex: wordEnd,
//                 coordinates: {
//                   ...textItem.coordinates,
//                   x: preciseX,
//                   width: preciseWidth,
//                   height: textItem.coordinates.height + 4, // Slightly taller for better visibility
//                 },
//               };

//               setCurrentHighlight(highlightItem);
//               setCurrentHighlightedWord(textItem);

//               // Track read words for progress indication
//               setReadWords((prev) =>
//                 new Set(prev).add(
//                   `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
//                 )
//               );
//             }
//           }
//         },
//         () => {
//           setCurrentHighlight(null);
//           setCurrentHighlightedWord(null);
//           handleReadingComplete();
//         }
//       );
//     }, 300);

//     return () => clearTimeout(readingTimeout);
//   }, [
//     isAutoReading,
//     documentInfo,
//     speaking,
//     isLoading,
//     getPageTextAndItems,
//     speak,
//     handleReadingComplete,
//   ]);

//   return (
//     <Box className="min-h-screen flex flex-col bg-gray-100">
//       <Header onHistoryClick={() => {}} />
//       <Container
//         maxWidth={false}
//         sx={{
//           flexGrow: 1,
//           py: { xs: 2, sm: 3, md: 4, lg: 4, xl: 4 },
//           px: { xs: 1, sm: 1, md: 2, lg: 2, xl: 3 },
//           display: "flex",
//           flexDirection: "column",
//           maxWidth: {
//             xs: "100%",
//             sm: "90%",
//             md: "95%",
//             lg: "90%",
//             xl: "85%",
//           },
//         }}
//       >
//         <Box
//           sx={{
//             display: "flex",
//             flexDirection: { xs: "column", md: "row" },
//             gap: { xs: 1, sm: 2, md: 2, lg: 2, xl: 2 },
//             height: "100%",
//           }}
//         >
//           <Box
//             sx={{
//               width: {
//                 xs: "100%",
//                 sm: "100%",
//                 md: "35%",
//                 lg: "30%",
//                 xl: "30%",
//               },
//               display: "flex",
//               flexDirection: "column",
//             }}
//           >
//             <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
//               <PageTitle title="Audio Controls" />
//               {/* {isStoppedAndReady && (
//                 <Typography
//                   variant="body2"
//                   align="center"
//                   sx={{
//                     color: 'success.main',
//                     fontWeight: 'bold',
//                     mb: 1,
//                     backgroundColor: 'success.light',
//                     borderRadius: 1,
//                     py: 0.5,
//                     px: 1,
//                   }}
//                 >
//                   🎯 Hover over any word to start reading
//                 </Typography>
//               )}
//               {(!speaking || paused) && !isStoppedAndReady && (
//                 <Typography
//                   variant="body2"
//                   align="center"
//                   sx={{
//                     color: paused ? 'warning.main' : 'info.main',
//                     fontWeight: 'bold',
//                     mb: 1,
//                     backgroundColor: paused ? 'warning.light' : 'info.light',
//                     borderRadius: 1,
//                     py: 0.5,
//                     px: 1,
//                   }}
//                 >
//                   {paused ? '⏸️ Hover, click or highlight text to resume reading' : '📝 Hover, click or highlight text to start reading'}
//                 </Typography>
//               )} */}
//               <Paper
//                 sx={{
//                   mt: 2,
//                   bgcolor: "transparent",
//                   p: { xs: 1, sm: 2, md: 2 },
//                 }}
//               >
//                 <AudioControls
//                   playing={speaking}
//                   paused={paused}
//                   onPlay={() => {
//                     setIsAutoReading(true);
//                     setIsStoppedAndReady(false); // Disable hover-to-read mode
//                   }}
//                   onPause={() => {
//                     pause();
//                     // Enable hover-to-read mode when paused
//                     setIsStoppedAndReady(true);
//                   }}
//                   onResume={() => {
//                     resume();
//                     // Disable hover-to-read mode when resuming
//                     setIsStoppedAndReady(false);
//                   }}
//                   onStop={() => {
//                     stop();
//                     setIsAutoReading(false);
//                     clearHighlights();
//                     setCurrentHighlight(null);
//                     setCurrentHighlightedWord(null);
//                     setIsStoppedAndReady(true); // Enable hover-to-read mode
//                   }}
//                   onNext={async () => {
//                     if (!documentInfo) return;
//                     stop();
//                     setIsAutoReading(false);
//                     clearHighlights();
//                     setCurrentHighlight(null);
//                     setCurrentHighlightedWord(null);
//                     setIsStoppedAndReady(false); // Disable hover-to-read mode
//                     const nextPage = documentInfo.currentPage + 1;
//                     if (nextPage <= documentInfo.totalPages) {
//                       await setCurrentPage(nextPage);
//                     }
//                   }}
//                   onPrevious={async () => {
//                     if (!documentInfo) return;
//                     stop();
//                     setIsAutoReading(false);
//                     clearHighlights();
//                     setCurrentHighlight(null);
//                     setCurrentHighlightedWord(null);
//                     setIsStoppedAndReady(false); // Disable hover-to-read mode
//                     const prevPage = documentInfo.currentPage - 1;
//                     if (prevPage >= 1) {
//                       await setCurrentPage(prevPage);
//                     }
//                   }}
//                   voiceOptions={options}
//                   setVoiceOptions={setOptions}
//                   availableVoices={voices}
//                 />
//               </Paper>
//             </Paper>
//           </Box>

//           <Box
//             sx={{
//               width: {
//                 xs: "100%",
//                 sm: "100%",
//                 md: "65%",
//                 lg: "70%",
//                 xl: "70%",
//               },
//               flexGrow: 1,
//             }}
//             ref={containerRef}
//           >
//             <PDFViewer
//               document={documentInfo}
//               highlights={highlights}
//               onPageChange={setCurrentPage}
//               onTextSelection={handleTextSelection}
//               currentHighlight={currentHighlight}
//               pageHeight={documentInfo?.pageHeight || 0}
//               isReading={isAutoReading || speaking}
//               textItems={spreadTextItems}
//               currentHighlightedWord={currentHighlightedWord}
//               onWordClick={handleWordClick}
//               onSentenceClick={handleSentenceClick}
//               onParagraphClick={handleParagraphClick}
//               onWordHover={handleWordHover}
//               onSentenceHover={handleSentenceHover}
//               onParagraphHover={handleParagraphHover}
//               speaking={speaking}
//               paused={paused}
//               readWords={readWords}
//             />
//           </Box>
//         </Box>
//       </Container>
//       <Footer />
//     </Box>
//   );
// };

// export default HomePage;

// import React, { useState, useEffect, useCallback, useRef } from "react";
// import { Container, Box, Paper, Typography } from "@mui/material";
// import PDFViewer from "../components/PDFViewer";
// import AudioControls from "../components/AudioControls";
// import Header from "../components/Header";
// import Footer from "../components/Footer";
// import { usePDFDocument } from "../hooks/usePDFDocument";
// import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
// import type { TextItem } from "../types";
// import { extractTextFromPage } from "../utils/pdfHelpers";
// import { PageTitle } from "../components/page-title-underline";
// import { Mail, Phone, MapPin, Clock } from "lucide-react";

// const HomePage: React.FC = () => {
//   const {
//     documentInfo,
//     currentPageText,
//     highlights,
//     loadDocument,
//     setCurrentPage,
//     addHighlight,
//     clearHighlights,
//     isLoading,
//   } = usePDFDocument();

//   const {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions,
//     speakFromPosition,
//   } = useSpeechSynthesis();

//   const [currentHighlightedText, setCurrentHighlightedText] = useState("");
//   const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(null);
//   const [currentHighlightedWord, setCurrentHighlightedWord] = useState<TextItem | null>(null);
//   const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
//   const [isAutoReading, setIsAutoReading] = useState(false);
//   const [isStoppedAndReady, setIsStoppedAndReady] = useState(false);
//   const [readWords, setReadWords] = useState<Set<string>>(new Set());
//   const [readPages, setReadPages] = useState<Set<number>>(new Set());
//   const [hoveredReadingPosition, setHoveredReadingPosition] = useState<{
//     text: string;
//     startIndex: number;
//     type: "word" | "sentence" | "paragraph";
//     textItem?: TextItem;
//   } | null>(null);
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     loadDocument("/src/assets/sample.pdf");
//   }, [loadDocument]);

//   const getPageTextAndItems = useCallback(
//     async (pageNumber: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc)
//         return { text: "", textItems: [], pageIndex: pageNumber };

//       const { fullText, textItems: pageTextItems } = await extractTextFromPage(
//         documentInfo.pdfDoc,
//         pageNumber
//       );

//       return { text: fullText.trim(), textItems: pageTextItems, pageIndex: pageNumber };
//     },
//     [documentInfo]
//   );

//   const getSpreadTextAndItems = useCallback(
//     async (leftPage: number, rightPage: number | null) => {
//       let combinedText = "";
//       let combinedTextItems: TextItem[] = [];
//       let pagesRead: number[] = [];

//       // Get text for left page
//       const leftResult = await getPageTextAndItems(leftPage);
//       if (leftResult.text) {
//         combinedText += leftResult.text;
//         combinedTextItems = [...combinedTextItems, ...leftResult.textItems.map(item => ({
//           ...item,
//           pageIndex: leftPage - 1
//         }))];
//         pagesRead.push(leftPage);
//       }

//       // Get text for right page if it exists
//       if (rightPage && rightPage <= (documentInfo?.totalPages || 0)) {
//         const rightResult = await getPageTextAndItems(rightPage);
//         if (rightResult.text) {
//           combinedText += combinedText ? ` ${rightResult.text}` : rightResult.text;
//           combinedTextItems = [...combinedTextItems, ...rightResult.textItems.map(item => ({
//             ...item,
//             pageIndex: rightPage - 1
//           }))];
//           pagesRead.push(rightPage);
//         }
//       }

//       console.log(`Spread ${leftPage}-${rightPage}:`, { text: combinedText, textItems: combinedTextItems, pagesRead });

//       return { text: combinedText, textItems: combinedTextItems, pagesRead };
//     },
//     [getPageTextAndItems, documentInfo]
//   );

//   const findNextNonBlankPage = useCallback(
//     async (startPage: number): Promise<number> => {
//       if (!documentInfo || !documentInfo.pdfDoc) return startPage;

//       for (let page = startPage; page <= documentInfo.totalPages; page++) {
//         const { text } = await getPageTextAndItems(page);
//         if (text && !readPages.has(page)) {
//           return page;
//         }
//       }
//       return documentInfo.totalPages + 1; // No non-blank pages left
//     },
//     [documentInfo, getPageTextAndItems, readPages]
//   );

//   const handleTextSelection = useCallback(
//     async (text: string, pageIndex: number) => {
//       if (!text || !documentInfo || !documentInfo.pdfDoc) return;

//       const isClickable = !speaking || paused;
//       if (!isClickable) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text: fullText, textItems } = await getPageTextAndItems(pageIndex + 1);

//       const selectedTextIndex = fullText.indexOf(text);
//       if (selectedTextIndex === -1) {
//         setCurrentHighlightedText(text);
//         addHighlight({ pageIndex, text });
//         speak(
//           text,
//           [],
//           pageIndex,
//           (event) => {
//             if (
//               event.charIndex !== undefined &&
//               event.charLength !== undefined
//             ) {
//               const charIndex = event.charIndex;
//               const currentTextItem = textItems.find(
//                 (item) =>
//                   charIndex >= item.startIndex && charIndex < item.endIndex
//               );
//               if (currentTextItem) {
//                 setCurrentHighlightedWord(currentTextItem);
//               }
//             }
//           },
//           () => {
//             setCurrentHighlightedWord(null);
//           }
//         );
//         return;
//       }

//       setCurrentHighlightedText(text);
//       addHighlight({ pageIndex, text });
//       speakFromPosition(
//         fullText,
//         selectedTextIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [
//       documentInfo,
//       speaking,
//       paused,
//       stop,
//       getPageTextAndItems,
//       speakFromPosition,
//       addHighlight,
//       speak,
//     ]
//   );

//   const handleWordClick = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

//       const wordStartIndex = textItem.startIndex;

//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         textItem.pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceClick = useCallback(
//     async (sentence: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleParagraphClick = useCallback(
//     async (paragraph: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleWordHover = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (textItem) {
//         setHoveredReadingPosition({
//           text: word,
//           startIndex: textItem.startIndex,
//           type: "word",
//           textItem: textItem,
//         });
//       } else {
//         setHoveredReadingPosition(null);
//       }

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

//       const wordStartIndex = textItem.startIndex;

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         textItem.pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceHover = useCallback(
//     async (sentence: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleParagraphHover = useCallback(
//     async (paragraph: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleReadingComplete = useCallback(async () => {
//     if (!documentInfo || !isAutoReading) return;

//     const currentPage = documentInfo.currentPage;
//     const isCoverPage = currentPage === 1;
    
//     // After cover page, go to page 2 (which will show pages 2-3 as a spread)
//     // After a spread, go to the next spread (current page + 2)
//     let nextPage = isCoverPage ? 2 : currentPage + 2;

//     // Find the next non-blank page
//     nextPage = await findNextNonBlankPage(nextPage);

//     if (nextPage > documentInfo.totalPages) {
//       // Reached the end of the document
//       setIsAutoReading(false);
//       stop();
//       clearHighlights();
//       setCurrentHighlight(null);
//       setCurrentHighlightedWord(null);
//       setReadWords(new Set());
//       setReadPages(new Set()); // Reset read pages when document is finished
//       setIsStoppedAndReady(true);
//       console.log("Document reading complete");
//     } else {
//       // Move to the next page or spread
//       stop();
//       setReadWords(new Set()); // Clear read words for new pages
//       await setCurrentPage(nextPage);
//       setIsAutoReading(true); // Continue auto-reading
//       console.log(`Moving to next spread starting with page ${nextPage}`);
//     }
//   }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights, findNextNonBlankPage]);

//   useEffect(() => {
//     if (
//       !isAutoReading ||
//       !documentInfo ||
//       speaking ||
//       isLoading ||
//       !documentInfo.pdfDoc
//     )
//       return;

//     const readingTimeout = setTimeout(async () => {
//       const currentPage = documentInfo.currentPage;
//       const isCoverPage = currentPage === 1;
      
//       // For non-cover pages, if current page is odd, make it even (left page of spread)
//       const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
//       // For cover page, there's no right page. For other pages, right page is left page + 1
//       const rightPage = isCoverPage ? null : leftPage + 1 <= documentInfo.totalPages ? leftPage + 1 : null;

//       console.log(`Current spread: ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

//       // Skip if all pages in the spread are already read or blank
//       const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
//       if (!text || pagesRead.every((page) => readPages.has(page))) {
//         console.log("All pages in spread already read or blank, moving to next spread");
//         await handleReadingComplete();
//         return;
//       }

//       setSpreadTextItems(textItems);
//       setCurrentHighlightedText(text);

//       // Separate text items by page
//       const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
//       const rightPageItems = rightPage ? textItems.filter(item => item.pageIndex === rightPage - 1) : [];

//       const readPage = async (pageText: string, pageItems: TextItem[], pageIndex: number) => {
//         if (!pageText) {
//           console.log(`No text on page ${pageIndex + 1}, skipping`);
//           return;
//         }
        
//         // Check if page is already read
//         if (readPages.has(pageIndex + 1)) {
//           console.log(`Page ${pageIndex + 1} already read, skipping`);
//           return;
//         }
        
//         console.log(`Reading page ${pageIndex + 1}`);
//         await new Promise<void>((resolve) => {
//           speak(
//             pageText,
//             [],
//             pageIndex,
//             (event) => {
//               if (event.charIndex !== undefined && event.charLength !== undefined) {
//                 const charIndex = event.charIndex;
//                 const textItem = pageItems.find(
//                   (item) =>
//                     charIndex >= item.startIndex && charIndex < item.endIndex
//                 );
//                 if (textItem) {
//                   const word = pageText
//                     .substring(charIndex, charIndex + event.charLength)
//                     .trim();

//                   const wordStart = Math.max(charIndex, textItem.startIndex);
//                   const wordEnd = Math.min(
//                     charIndex + event.charLength,
//                     textItem.endIndex
//                   );

//                   const textItemLength = Math.max(
//                     textItem.endIndex - textItem.startIndex,
//                     1
//                   );
//                   const wordLength = Math.max(wordEnd - wordStart, 1);

//                   const wordProgress =
//                     (wordStart - textItem.startIndex) / textItemLength;
//                   const wordLengthRatio = wordLength / textItemLength;

//                   const clampedProgress = Math.max(0, Math.min(1, wordProgress));
//                   const clampedLengthRatio = Math.max(
//                     0.1,
//                     Math.min(1, wordLengthRatio)
//                   );

//                   const preciseX =
//                     textItem.coordinates.x +
//                     textItem.coordinates.width * clampedProgress;
//                   const preciseWidth = Math.max(
//                     textItem.coordinates.width * clampedLengthRatio,
//                     15
//                   );

//                   const highlightItem = {
//                     ...textItem,
//                     text: word,
//                     startIndex: wordStart,
//                     endIndex: wordEnd,
//                     coordinates: {
//                       ...textItem.coordinates,
//                       x: preciseX,
//                       width: preciseWidth,
//                       height: textItem.coordinates.height + 4,
//                     },
//                   };

//                   setCurrentHighlight(highlightItem);
//                   setCurrentHighlightedWord(textItem);

//                   setReadWords((prev) =>
//                     new Set(prev).add(
//                       `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
//                     )
//                   );
//                 }
//               }
//             },
//             () => {
//               setCurrentHighlight(null);
//               setCurrentHighlightedWord(null);
//               resolve();
//             }
//           );
//         });
//       };

//       try {
//         // Read cover page or left page first
//         const leftText = leftPageItems.length > 0 ? (await getPageTextAndItems(leftPage)).text : "";
//         await readPage(leftText, leftPageItems, leftPage - 1);
        
//         // Mark left page as read
//         setReadPages(prev => new Set([...prev, leftPage]));

//         // Read right page if it exists and we're not on the cover page
//         if (!isCoverPage && rightPage && rightPageItems.length > 0) {
//           const rightText = (await getPageTextAndItems(rightPage)).text;
//           await readPage(rightText, rightPageItems, rightPage - 1);
          
//           // Mark right page as read
//           setReadPages(prev => new Set([...prev, rightPage]));
//         }

//         // After reading all pages in the spread, move to the next spread
//         console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
//         handleReadingComplete();
//       } catch (error) {
//         console.error("Error during auto-reading:", error);
//         // If there's an error, still try to move to the next spread
//         handleReadingComplete();
//       }
//     }, 500); // Slightly longer timeout to ensure proper page loading

//     return () => clearTimeout(readingTimeout);
//   }, [
//     isAutoReading,
//     documentInfo,
//     speaking,
//     isLoading,
//     getSpreadTextAndItems,
//     getPageTextAndItems,
//     speak,
//     handleReadingComplete,
//     readPages,
//   ]);

//   return (
//     <Box className="min-h-screen flex flex-col bg-gray-100">
//       <Header onHistoryClick={() => {}} />
//       <Container
//         maxWidth={false}
//         sx={{
//           flexGrow: 1,
//           py: { xs: 2, sm: 3, md: 4, lg: 4, xl: 4 },
//           px: { xs: 1, sm: 1, md: 2, lg: 2, xl: 3 },
//           display: "flex",
//           flexDirection: "column",
//           maxWidth: {
//             xs: "100%",
//             sm: "90%",
//             md: "95%",
//             lg: "90%",
//             xl: "85%",
//           },
//         }}
//       >
//         <Box
//           sx={{
//             display: "flex",
//             flexDirection: { xs: "column", md: "row" },
//             gap: { xs: 1, sm: 2, md: 2, lg: 2, xl: 2 },
//             height: "100%",
//           }}
//         >
//           <Box
//             sx={{
//               width: {
//                 xs: "100%",
//                 sm: "100%",
//                 md: "35%",
//                 lg: "30%",
//                 xl: "30%",
//               },
//               display: "flex",
//               flexDirection: "column",
//             }}
//           >
//             <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
//               <PageTitle title="Audio Controls" />
//               <Paper
//                 sx={{
//                   mt: 2,
//                   bgcolor: "transparent",
//                   p: { xs: 1, sm: 2, md: 2 },
//                 }}
//               >
//                 <AudioControls
//                   playing={speaking}
//                   paused={paused}
//                   onPlay={async () => {
//                     if (speaking) {
//                       stop();
//                     }
                    
//                     // Reset any previously read pages if starting fresh
//                     if (!isAutoReading) {
//                       setReadPages(new Set());
//                     }
                    
//                     setIsAutoReading(true);
//                     setIsStoppedAndReady(false);
                    
//                     const currentPage = documentInfo?.currentPage || 1;
//                     const isCoverPage = currentPage === 1;
                    
//                     // For non-cover pages, if current page is odd, make it even (left page of spread)
//                     const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
//                     // For cover page, there's no right page. For other pages, right page is left page + 1
//                     const rightPage = isCoverPage ? null : leftPage + 1 <= (documentInfo?.totalPages || 0) ? leftPage + 1 : null;

//                     console.log(`Starting to read ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

//                     // Skip if all pages in the spread are already read or blank
//                     const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
//                     if (!text || pagesRead.every((page) => readPages.has(page))) {
//                       console.log("All pages in spread already read or blank, moving to next spread");
//                       await handleReadingComplete();
//                       return;
//                     }

//                     setSpreadTextItems(textItems);
//                     setCurrentHighlightedText(text);

//                     // Function to read a single page
//                     const readPage = async (pageNum: number, pageItems: TextItem[]) => {
//                       if (readPages.has(pageNum)) {
//                         console.log(`Page ${pageNum} already read, skipping`);
//                         return;
//                       }
                      
//                       const pageText = (await getPageTextAndItems(pageNum)).text;
//                       if (!pageText) {
//                         console.log(`No text on page ${pageNum}, skipping`);
//                         return;
//                       }
                      
//                       console.log(`Reading page ${pageNum}`);
//                       await new Promise<void>((resolve) => {
//                         speak(
//                           pageText,
//                           [],
//                           pageNum - 1,
//                           (event) => {
//                             if (event.charIndex !== undefined && event.charLength !== undefined) {
//                               const charIndex = event.charIndex;
//                               const textItem = pageItems.find(
//                                 (item) =>
//                                   charIndex >= item.startIndex && charIndex < item.endIndex
//                               );
//                               if (textItem) {
//                                 setCurrentHighlightedWord(textItem);
//                                 setReadWords((prev) =>
//                                   new Set(prev).add(
//                                     `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
//                                   )
//                                 );
//                               }
//                             }
//                           },
//                           () => {
//                             setCurrentHighlightedWord(null);
//                             resolve();
//                           }
//                         );
//                       });
                      
//                       // Mark page as read
//                       setReadPages(prev => new Set([...prev, pageNum]));
//                     };

//                     try {
//                       // Read left page (or cover page)
//                       const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
//                       await readPage(leftPage, leftPageItems);
                      
//                       // Read right page if it exists and we're not on the cover page
//                       if (!isCoverPage && rightPage) {
//                         const rightPageItems = textItems.filter(item => item.pageIndex === rightPage - 1);
//                         await readPage(rightPage, rightPageItems);
//                       }
                      
//                       // After reading all pages in the spread, move to the next spread
//                       console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
//                       handleReadingComplete();
//                     } catch (error) {
//                       console.error("Error during reading:", error);
//                       // If there's an error, still try to move to the next spread
//                       handleReadingComplete();
//                     }
//                   }}
//                   onPause={() => {
//                     pause();
//                     setIsStoppedAndReady(true);
//                   }}
//                   onResume={() => {
//                     resume();
//                     setIsStoppedAndReady(false);
//                   }}
//                   onStop={() => {
//                     stop();
//                     setIsAutoReading(false);
//                     clearHighlights();
//                     setCurrentHighlight(null);
//                     setCurrentHighlightedWord(null);
//                     setIsStoppedAndReady(true);
//                     setReadPages(new Set()); // Reset read pages on stop
//                   }}
//                   onNext={async () => {
//                     if (!documentInfo) return;
//                     stop();
//                     setIsAutoReading(false);
//                     clearHighlights();
//                     setCurrentHighlight(null);
//                     setCurrentHighlightedWord(null);
//                     setIsStoppedAndReady(false);
                    
//                     const currentPage = documentInfo.currentPage;
//                     const isCoverPage = currentPage === 1;
                    
//                     // After cover page, go to page 2 (which will show pages 2-3 as a spread)
//                     // After a spread, go to the next spread (current page + 2 if even, or current page + 1 if odd)
//                     let nextPage;
//                     if (isCoverPage) {
//                       nextPage = 2; // After cover, always go to page 2 (first spread)
//                     } else {
//                       // Ensure we're moving to the start of the next spread (even page number)
//                       nextPage = currentPage % 2 === 0 ? currentPage + 2 : currentPage + 1;
//                     }
                    
//                     // Find the next non-blank page
//                     const nonBlankNextPage = await findNextNonBlankPage(nextPage);
                    
//                     if (nonBlankNextPage <= documentInfo.totalPages) {
//                       console.log(`Navigating to next spread starting with page ${nonBlankNextPage}`);
//                       await setCurrentPage(nonBlankNextPage);
//                       setIsAutoReading(true);
//                     } else {
//                       console.log("Reached end of document");
//                     }
//                   }}
//                   onPrevious={async () => {
//                     if (!documentInfo) return;
//                     stop();
//                     setIsAutoReading(false);
//                     clearHighlights();
//                     setCurrentHighlight(null);
//                     setCurrentHighlightedWord(null);
//                     setIsStoppedAndReady(false);
                    
//                     const currentPage = documentInfo.currentPage;
                    
//                     // If on page 2 or 3, go back to cover page
//                     // Otherwise, go to previous spread (current page - 2 if even, or current page - 3 if odd)
//                     let prevPage;
//                     if (currentPage <= 3) {
//                       prevPage = 1; // Go to cover page
//                     } else {
//                       // Ensure we're moving to the start of the previous spread (even page number)
//                       prevPage = currentPage % 2 === 0 ? currentPage - 2 : currentPage - 3;
//                     }
                    
//                     if (prevPage >= 1) {
//                       console.log(`Navigating to previous spread starting with page ${prevPage}`);
//                       await setCurrentPage(prevPage);
//                       setIsAutoReading(true);
//                     }
//                   }}
//                   voiceOptions={options}
//                   setVoiceOptions={setOptions}
//                   availableVoices={voices}
//                 />
//               </Paper>
//             </Paper>
//           </Box>

//           <Box
//             sx={{
//               width: {
//                 xs: "100%",
//                 sm: "100%",
//                 md: "65%",
//                 lg: "70%",
//                 xl: "70%",
//               },
//               flexGrow: 1,
//             }}
//             ref={containerRef}
//           >
//             <PDFViewer
//               document={documentInfo}
//               highlights={highlights}
//               onPageChange={setCurrentPage}
//               onTextSelection={handleTextSelection}
//               currentHighlight={currentHighlight}
//               pageHeight={documentInfo?.pageHeight || 0}
//               isReading={isAutoReading || speaking}
//               textItems={spreadTextItems}
//               currentHighlightedWord={currentHighlightedWord}
//               onWordClick={handleWordClick}
//               onSentenceClick={handleSentenceClick}
//               onParagraphClick={handleParagraphClick}
//               onWordHover={handleWordHover}
//               onSentenceHover={handleSentenceHover}
//               onParagraphHover={handleParagraphHover}
//               speaking={speaking}
//               paused={paused}
//               readWords={readWords}
//               isCoverPage={documentInfo?.currentPage === 1}
//             />
//           </Box>
//         </Box>
//       </Container>
//       <Footer />
//     </Box>
//   );
// };

// export default HomePage;

// import React, { useState, useEffect, useCallback, useRef } from "react";
// import { Container, Box, Paper, Typography, Button } from "@mui/material";
// import { SkipPrevious, PlayArrow, SkipNext, Stop } from "@mui/icons-material";
// import PDFViewer from "../components/PDFViewer";
// import AudioControls from "../components/AudioControls";
// import Header from "../components/Header";
// import Footer from "../components/Footer";
// import { usePDFDocument } from "../hooks/usePDFDocument";
// import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
// import type { TextItem } from "../types";
// import { extractTextFromPage } from "../utils/pdfHelpers";
// import { PageTitle } from "../components/page-title-underline";
// import { Mail, Phone, MapPin, Clock } from "lucide-react";

// const HomePage: React.FC = () => {
//   const {
//     documentInfo,
//     currentPageText,
//     highlights,
//     loadDocument,
//     setCurrentPage,
//     addHighlight,
//     clearHighlights,
//     isLoading,
//   } = usePDFDocument();

//   const {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions,
//     speakFromPosition,
//   } = useSpeechSynthesis();

//   const [currentHighlightedText, setCurrentHighlightedText] = useState("");
//   const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(null);
//   const [currentHighlightedWord, setCurrentHighlightedWord] = useState<TextItem | null>(null);
//   const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
//   const [isAutoReading, setIsAutoReading] = useState(false);
//   const [isStoppedAndReady, setIsStoppedAndReady] = useState(false);
//   const [readWords, setReadWords] = useState<Set<string>>(new Set());
//   const [readPages, setReadPages] = useState<Set<number>>(new Set());
//   const [hoveredReadingPosition, setHoveredReadingPosition] = useState<{
//     text: string;
//     startIndex: number;
//     type: "word" | "sentence" | "paragraph";
//     textItem?: TextItem;
//   } | null>(null);
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     loadDocument("/src/assets/sample.pdf");
//   }, [loadDocument]);

//   const getPageTextAndItems = useCallback(
//     async (pageNumber: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc)
//         return { text: "", textItems: [], pageIndex: pageNumber };

//       const { fullText, textItems: pageTextItems } = await extractTextFromPage(
//         documentInfo.pdfDoc,
//         pageNumber
//       );

//       return { text: fullText.trim(), textItems: pageTextItems, pageIndex: pageNumber };
//     },
//     [documentInfo]
//   );

//   const getSpreadTextAndItems = useCallback(
//     async (leftPage: number, rightPage: number | null) => {
//       let combinedText = "";
//       let combinedTextItems: TextItem[] = [];
//       let pagesRead: number[] = [];

//       const leftResult = await getPageTextAndItems(leftPage);
//       if (leftResult.text) {
//         combinedText += leftResult.text;
//         combinedTextItems = [...combinedTextItems, ...leftResult.textItems.map(item => ({
//           ...item,
//           pageIndex: leftPage - 1
//         }))];
//         pagesRead.push(leftPage);
//       }

//       if (rightPage && rightPage <= (documentInfo?.totalPages || 0)) {
//         const rightResult = await getPageTextAndItems(rightPage);
//         if (rightResult.text) {
//           combinedText += combinedText ? ` ${rightResult.text}` : rightResult.text;
//           combinedTextItems = [...combinedTextItems, ...rightResult.textItems.map(item => ({
//             ...item,
//             pageIndex: rightPage - 1
//           }))];
//           pagesRead.push(rightPage);
//         }
//       }

//       console.log(`Spread ${leftPage}-${rightPage}:`, { text: combinedText, textItems: combinedTextItems, pagesRead });

//       return { text: combinedText, textItems: combinedTextItems, pagesRead };
//     },
//     [getPageTextAndItems, documentInfo]
//   );

//   const findNextNonBlankPage = useCallback(
//     async (startPage: number): Promise<number> => {
//       if (!documentInfo || !documentInfo.pdfDoc) return startPage;

//       for (let page = startPage; page <= documentInfo.totalPages; page++) {
//         const { text } = await getPageTextAndItems(page);
//         if (text && !readPages.has(page)) {
//           return page;
//         }
//       }
//       return documentInfo.totalPages + 1;
//     },
//     [documentInfo, getPageTextAndItems, readPages]
//   );

//   const handleTextSelection = useCallback(
//     async (text: string, pageIndex: number) => {
//       if (!text || !documentInfo || !documentInfo.pdfDoc) return;

//       const isClickable = !speaking || paused;
//       if (!isClickable) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text: fullText, textItems } = await getPageTextAndItems(pageIndex + 1);

//       const selectedTextIndex = fullText.indexOf(text);
//       if (selectedTextIndex === -1) {
//         setCurrentHighlightedText(text);
//         addHighlight({ pageIndex, text });
//         speak(
//           text,
//           [],
//           pageIndex,
//           (event) => {
//             if (
//               event.charIndex !== undefined &&
//               event.charLength !== undefined
//             ) {
//               const charIndex = event.charIndex;
//               const currentTextItem = textItems.find(
//                 (item) =>
//                   charIndex >= item.startIndex && charIndex < item.endIndex
//               );
//               if (currentTextItem) {
//                 setCurrentHighlightedWord(currentTextItem);
//               }
//             }
//           },
//           () => {
//             setCurrentHighlightedWord(null);
//           }
//         );
//         return;
//       }

//       setCurrentHighlightedText(text);
//       addHighlight({ pageIndex, text });
//       speakFromPosition(
//         fullText,
//         selectedTextIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [
//       documentInfo,
//       speaking,
//       paused,
//       stop,
//       getPageTextAndItems,
//       speakFromPosition,
//       addHighlight,
//       speak,
//     ]
//   );

//   const handleWordClick = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

//       const wordStartIndex = textItem.startIndex;

//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         textItem.pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceClick = useCallback(
//     async (sentence: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleParagraphClick = useCallback(
//     async (paragraph: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleWordHover = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (textItem) {
//         setHoveredReadingPosition({
//           text: word,
//           startIndex: textItem.startIndex,
//           type: "word",
//           textItem: textItem,
//         });
//       } else {
//         setHoveredReadingPosition(null);
//       }

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

//       const wordStartIndex = textItem.startIndex;

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         textItem.pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceHover = useCallback(
//     async (sentence: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleParagraphHover = useCallback(
//     async (paragraph: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleReadingComplete = useCallback(async () => {
//     if (!documentInfo || !isAutoReading) return;

//     const currentPage = documentInfo.currentPage;
//     const isCoverPage = currentPage === 1;
    
//     let nextPage = isCoverPage ? 2 : currentPage + 2;

//     nextPage = await findNextNonBlankPage(nextPage);

//     if (nextPage > documentInfo.totalPages) {
//       setIsAutoReading(false);
//       stop();
//       clearHighlights();
//       setCurrentHighlight(null);
//       setCurrentHighlightedWord(null);
//       setReadWords(new Set());
//       setReadPages(new Set());
//       setIsStoppedAndReady(true);
//       console.log("Document reading complete");
//     } else {
//       stop();
//       setReadWords(new Set());
//       await setCurrentPage(nextPage);
//       setIsAutoReading(true);
//       console.log(`Moving to next spread starting with page ${nextPage}`);
//     }
//   }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights, findNextNonBlankPage]);

//   useEffect(() => {
//     if (
//       !isAutoReading ||
//       !documentInfo ||
//       speaking ||
//       isLoading ||
//       !documentInfo.pdfDoc
//     )
//       return;

//     const readingTimeout = setTimeout(async () => {
//       const currentPage = documentInfo.currentPage;
//       const isCoverPage = currentPage === 1;
      
//       const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
//       const rightPage = isCoverPage ? null : leftPage + 1 <= documentInfo.totalPages ? leftPage + 1 : null;

//       console.log(`Current spread: ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

//       const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
//       if (!text || pagesRead.every((page) => readPages.has(page))) {
//         console.log("All pages in spread already read or blank, moving to next spread");
//         await handleReadingComplete();
//         return;
//       }

//       setSpreadTextItems(textItems);
//       setCurrentHighlightedText(text);

//       const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
//       const rightPageItems = rightPage ? textItems.filter(item => item.pageIndex === rightPage - 1) : [];

//       const readPage = async (pageText: string, pageItems: TextItem[], pageIndex: number) => {
//         if (!pageText) {
//           console.log(`No text on page ${pageIndex + 1}, skipping`);
//           return;
//         }
        
//         if (readPages.has(pageIndex + 1)) {
//           console.log(`Page ${pageIndex + 1} already read, skipping`);
//           return;
//         }
        
//         console.log(`Reading page ${pageIndex + 1}`);
//         await new Promise<void>((resolve) => {
//           speak(
//             pageText,
//             [],
//             pageIndex,
//             (event) => {
//               if (event.charIndex !== undefined && event.charLength !== undefined) {
//                 const charIndex = event.charIndex;
//                 const textItem = pageItems.find(
//                   (item) =>
//                     charIndex >= item.startIndex && charIndex < item.endIndex
//                 );
//                 if (textItem) {
//                   const word = pageText
//                     .substring(charIndex, charIndex + event.charLength)
//                     .trim();

//                   const wordStart = Math.max(charIndex, textItem.startIndex);
//                   const wordEnd = Math.min(
//                     charIndex + event.charLength,
//                     textItem.endIndex
//                   );

//                   const textItemLength = Math.max(
//                     textItem.endIndex - textItem.startIndex,
//                     1
//                   );
//                   const wordLength = Math.max(wordEnd - wordStart, 1);

//                   const wordProgress =
//                     (wordStart - textItem.startIndex) / textItemLength;
//                   const wordLengthRatio = wordLength / textItemLength;

//                   const clampedProgress = Math.max(0, Math.min(1, wordProgress));
//                   const clampedLengthRatio = Math.max(
//                     0.1,
//                     Math.min(1, wordLengthRatio)
//                   );

//                   const preciseX =
//                     textItem.coordinates.x +
//                     textItem.coordinates.width * clampedProgress;
//                   const preciseWidth = Math.max(
//                     textItem.coordinates.width * clampedLengthRatio,
//                     15
//                   );

//                   const highlightItem = {
//                     ...textItem,
//                     text: word,
//                     startIndex: wordStart,
//                     endIndex: wordEnd,
//                     coordinates: {
//                       ...textItem.coordinates,
//                       x: preciseX,
//                       width: preciseWidth,
//                       height: textItem.coordinates.height + 4,
//                     },
//                   };

//                   setCurrentHighlight(highlightItem);
//                   setCurrentHighlightedWord(textItem);

//                   setReadWords((prev) =>
//                     new Set(prev).add(
//                       `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
//                     )
//                   );
//                 }
//               }
//             },
//             () => {
//               setCurrentHighlight(null);
//               setCurrentHighlightedWord(null);
//               resolve();
//             }
//           );
//         });
//       };

//       try {
//         const leftText = leftPageItems.length > 0 ? (await getPageTextAndItems(leftPage)).text : "";
//         await readPage(leftText, leftPageItems, leftPage - 1);
        
//         setReadPages(prev => new Set([...prev, leftPage]));

//         if (!isCoverPage && rightPage && rightPageItems.length > 0) {
//           const rightText = (await getPageTextAndItems(rightPage)).text;
//           await readPage(rightText, rightPageItems, rightPage - 1);
          
//           setReadPages(prev => new Set([...prev, rightPage]));
//         }

//         console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
//         handleReadingComplete();
//       } catch (error) {
//         console.error("Error during auto-reading:", error);
//         handleReadingComplete();
//       }
//     }, 500);

//     return () => clearTimeout(readingTimeout);
//   }, [
//     isAutoReading,
//     documentInfo,
//     speaking,
//     isLoading,
//     getSpreadTextAndItems,
//     getPageTextAndItems,
//     speak,
//     handleReadingComplete,
//     readPages,
//   ]);

//   return (
//     <Box className="min-h-screen flex flex-col bg-gray-100">
//       <Header onHistoryClick={() => {}} />
//       <Container
//         maxWidth={false}
//         sx={{
//           py: { xs: 1, sm: 2, md: 2 },
//           px: { xs: 1, sm: 1, md: 2, lg: 2, xl: 3 },
//           display: "flex",
//           flexDirection: "row",
//           height: { xs: "85vh", sm: "85vh", md: "90vh", lg: "90vh", xl: "90vh" },
//           overflow: "hidden",
//         }}
//       >
//         <Box
//           sx={{
//             display: "flex",
//             flexDirection: "column",
//             justifyContent: "space-around",
//             alignItems: "center",
//             width: { xs: "20%", sm: "20%", md: "15%", lg: "15%", xl: "15%" },
//             height: "100%",
//             //p: 2,
//           }}
//         >
//           <Paper elevation={3} sx={{ p: 2, width: "100%" }}>
//             <PageTitle title="Audio Controls" sx={{ textAlign: "center" }} />
//             <Box sx={{ display: "flex", flexDirection: "column", gap: 2, mt: 2 }}>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<SkipPrevious />}
//                 onClick={async () => {
//                   if (!documentInfo) return;
//                   stop();
//                   setIsAutoReading(false);
//                   clearHighlights();
//                   setCurrentHighlight(null);
//                   setCurrentHighlightedWord(null);
//                   setIsStoppedAndReady(false);
                  
//                   const currentPage = documentInfo.currentPage;
                  
//                   let prevPage;
//                   if (currentPage <= 3) {
//                     prevPage = 1;
//                   } else {
//                     prevPage = currentPage % 2 === 0 ? currentPage - 2 : currentPage - 3;
//                   }
                  
//                   if (prevPage >= 1) {
//                     console.log(`Navigating to previous spread starting with page ${prevPage}`);
//                     await setCurrentPage(prevPage);
//                     setIsAutoReading(true);
//                   }
//                 }}
//               >
//                 Previous
//               </Button>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<PlayArrow />}
//                 onClick={async () => {
//                   if (speaking) {
//                     stop();
//                   }
                  
//                   if (!isAutoReading) {
//                     setReadPages(new Set());
//                   }
                  
//                   setIsAutoReading(true);
//                   setIsStoppedAndReady(false);
                  
//                   const currentPage = documentInfo?.currentPage || 1;
//                   const isCoverPage = currentPage === 1;
                  
//                   const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
//                   const rightPage = isCoverPage ? null : leftPage + 1 <= (documentInfo?.totalPages || 0) ? leftPage + 1 : null;

//                   console.log(`Starting to read ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

//                   const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
//                   if (!text || pagesRead.every((page) => readPages.has(page))) {
//                     console.log("All pages in spread already read or blank, moving to next spread");
//                     await handleReadingComplete();
//                     return;
//                   }

//                   setSpreadTextItems(textItems);
//                   setCurrentHighlightedText(text);

//                   const readPage = async (pageNum: number, pageItems: TextItem[]) => {
//                     if (readPages.has(pageNum)) {
//                       console.log(`Page ${pageNum} already read, skipping`);
//                       return;
//                     }
                    
//                     const pageText = (await getPageTextAndItems(pageNum)).text;
//                     if (!pageText) {
//                       console.log(`No text on page ${pageNum}, skipping`);
//                       return;
//                     }
                    
//                     console.log(`Reading page ${pageNum}`);
//                     await new Promise<void>((resolve) => {
//                       speak(
//                         pageText,
//                         [],
//                         pageNum - 1,
//                         (event) => {
//                           if (event.charIndex !== undefined && event.charLength !== undefined) {
//                             const charIndex = event.charIndex;
//                             const textItem = pageItems.find(
//                               (item) =>
//                                 charIndex >= item.startIndex && charIndex < item.endIndex
//                             );
//                             if (textItem) {
//                               setCurrentHighlightedWord(textItem);
//                               setReadWords((prev) =>
//                                 new Set(prev).add(
//                                   `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
//                                 )
//                               );
//                             }
//                           }
//                         },
//                         () => {
//                           setCurrentHighlightedWord(null);
//                           resolve();
//                         }
//                       );
//                     });
                    
//                     setReadPages(prev => new Set([...prev, pageNum]));
//                   };

//                   try {
//                     const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
//                     await readPage(leftPage, leftPageItems);
                    
//                     if (!isCoverPage && rightPage) {
//                       const rightPageItems = textItems.filter(item => item.pageIndex === rightPage - 1);
//                       await readPage(rightPage, rightPageItems);
//                     }
                    
//                     console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
//                     handleReadingComplete();
//                   } catch (error) {
//                     console.error("Error during reading:", error);
//                     handleReadingComplete();
//                   }
//                 }}
//               >
//                 Play
//               </Button>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<SkipNext />}
//                 onClick={async () => {
//                   if (!documentInfo) return;
//                   stop();
//                   setIsAutoReading(false);
//                   clearHighlights();
//                   setCurrentHighlight(null);
//                   setCurrentHighlightedWord(null);
//                   setIsStoppedAndReady(false);
                  
//                   const currentPage = documentInfo.currentPage;
//                   const isCoverPage = currentPage === 1;
                  
//                   let nextPage;
//                   if (isCoverPage) {
//                     nextPage = 2;
//                   } else {
//                     nextPage = currentPage % 2 === 0 ? currentPage + 2 : currentPage + 1;
//                   }
                  
//                   const nonBlankNextPage = await findNextNonBlankPage(nextPage);
                  
//                   if (nonBlankNextPage <= documentInfo.totalPages) {
//                     console.log(`Navigating to next spread starting with page ${nonBlankNextPage}`);
//                     await setCurrentPage(nonBlankNextPage);
//                     setIsAutoReading(true);
//                   } else {
//                     console.log("Reached end of document");
//                   }
//                 }}
//               >
//                 Next
//               </Button>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<Stop />}
//                 onClick={() => {
//                   stop();
//                   setIsAutoReading(false);
//                   clearHighlights();
//                   setCurrentHighlight(null);
//                   setCurrentHighlightedWord(null);
//                   setIsStoppedAndReady(true);
//                   setReadPages(new Set());
//                 }}
//               >
//                 Stop
//               </Button>
//             </Box>
//           </Paper>
//         </Box>
//         <Box
//           sx={{
//             flexGrow: 1,
//             ml: { xs: "20%", sm: "20%", md: "15%", lg: "15%", xl: "15%" }, // Offset for audio controls
//           }}
//           ref={containerRef}
//         >
//           <PDFViewer
//             document={documentInfo}
//             highlights={highlights}
//             onPageChange={setCurrentPage}
//             onTextSelection={handleTextSelection}
//             currentHighlight={currentHighlight}
//             pageHeight={documentInfo?.pageHeight || 0}
//             isReading={isAutoReading || speaking}
//             textItems={spreadTextItems}
//             currentHighlightedWord={currentHighlightedWord}
//             onWordClick={handleWordClick}
//             onSentenceClick={handleSentenceClick}
//             onParagraphClick={handleParagraphClick}
//             onWordHover={handleWordHover}
//             onSentenceHover={handleSentenceHover}
//             onParagraphHover={handleParagraphHover}
//             speaking={speaking}
//             paused={paused}
//             readWords={readWords}
//             isCoverPage={documentInfo?.currentPage === 1}
//           />
//         </Box>
//       </Container>
//       <Footer />
//     </Box>
//   );
// };

// export default HomePage;

// import React, { useState, useEffect, useCallback, useRef } from "react";
// import { Container, Box, Paper, Typography, Button } from "@mui/material";
// import { SkipPrevious, PlayArrow, SkipNext, Stop } from "@mui/icons-material";
// import PDFViewer from "../components/PDFViewer";
// import AudioControls from "../components/AudioControls";
// import Header from "../components/Header";
// import Footer from "../components/Footer";
// import { usePDFDocument } from "../hooks/usePDFDocument";
// import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
// import type { TextItem } from "../types";
// import { extractTextFromPage } from "../utils/pdfHelpers";
// import { PageTitle } from "../components/page-title-underline";
// import { Mail, Phone, MapPin, Clock } from "lucide-react";

// const HomePage: React.FC = () => {
//   const {
//     documentInfo,
//     currentPageText,
//     highlights,
//     loadDocument,
//     setCurrentPage,
//     addHighlight,
//     clearHighlights,
//     isLoading,
//   } = usePDFDocument();

//   const {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions,
//     speakFromPosition,
//   } = useSpeechSynthesis();

//   const [currentHighlightedText, setCurrentHighlightedText] = useState("");
//   const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(null);
//   const [currentHighlightedWord, setCurrentHighlightedWord] = useState<TextItem | null>(null);
//   const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
//   const [isAutoReading, setIsAutoReading] = useState(false);
//   const [isStoppedAndReady, setIsStoppedAndReady] = useState(false);
//   const [readWords, setReadWords] = useState<Set<string>>(new Set());
//   const [readPages, setReadPages] = useState<Set<number>>(new Set());
//   const [hoveredReadingPosition, setHoveredReadingPosition] = useState<{
//     text: string;
//     startIndex: number;
//     type: "word" | "sentence" | "paragraph";
//     textItem?: TextItem;
//   } | null>(null);
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     loadDocument("/src/assets/sample.pdf");
//   }, [loadDocument]);

//   const getPageTextAndItems = useCallback(
//     async (pageNumber: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc)
//         return { text: "", textItems: [], pageIndex: pageNumber };

//       const { fullText, textItems: pageTextItems } = await extractTextFromPage(
//         documentInfo.pdfDoc,
//         pageNumber
//       );

//       return { text: fullText.trim(), textItems: pageTextItems, pageIndex: pageNumber };
//     },
//     [documentInfo]
//   );

//   const getSpreadTextAndItems = useCallback(
//     async (leftPage: number, rightPage: number | null) => {
//       let combinedText = "";
//       let combinedTextItems: TextItem[] = [];
//       let pagesRead: number[] = [];

//       const leftResult = await getPageTextAndItems(leftPage);
//       if (leftResult.text) {
//         combinedText += leftResult.text;
//         combinedTextItems = [...combinedTextItems, ...leftResult.textItems.map(item => ({
//           ...item,
//           pageIndex: leftPage - 1
//         }))];
//         pagesRead.push(leftPage);
//       }

//       if (rightPage && rightPage <= (documentInfo?.totalPages || 0)) {
//         const rightResult = await getPageTextAndItems(rightPage);
//         if (rightResult.text) {
//           combinedText += combinedText ? ` ${rightResult.text}` : rightResult.text;
//           combinedTextItems = [...combinedTextItems, ...rightResult.textItems.map(item => ({
//             ...item,
//             pageIndex: rightPage - 1
//           }))];
//           pagesRead.push(rightPage);
//         }
//       }

//       console.log(`Spread ${leftPage}-${rightPage}:`, { text: combinedText, textItems: combinedTextItems, pagesRead });

//       return { text: combinedText, textItems: combinedTextItems, pagesRead };
//     },
//     [getPageTextAndItems, documentInfo]
//   );

//   const findNextNonBlankPage = useCallback(
//     async (startPage: number): Promise<number> => {
//       if (!documentInfo || !documentInfo.pdfDoc) return startPage;

//       for (let page = startPage; page <= documentInfo.totalPages; page++) {
//         const { text } = await getPageTextAndItems(page);
//         if (text && !readPages.has(page)) {
//           return page;
//         }
//       }
//       return documentInfo.totalPages + 1;
//     },
//     [documentInfo, getPageTextAndItems, readPages]
//   );

//   const handleTextSelection = useCallback(
//     async (text: string, pageIndex: number) => {
//       if (!text || !documentInfo || !documentInfo.pdfDoc) return;

//       const isClickable = !speaking || paused;
//       if (!isClickable) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text: fullText, textItems } = await getPageTextAndItems(pageIndex + 1);

//       const selectedTextIndex = fullText.indexOf(text);
//       if (selectedTextIndex === -1) {
//         setCurrentHighlightedText(text);
//         addHighlight({ pageIndex, text });
//         speak(
//           text,
//           [],
//           pageIndex,
//           (event) => {
//             if (
//               event.charIndex !== undefined &&
//               event.charLength !== undefined
//             ) {
//               const charIndex = event.charIndex;
//               const currentTextItem = textItems.find(
//                 (item) =>
//                   charIndex >= item.startIndex && charIndex < item.endIndex
//               );
//               if (currentTextItem) {
//                 setCurrentHighlightedWord(currentTextItem);
//               }
//             }
//           },
//           () => {
//             setCurrentHighlightedWord(null);
//           }
//         );
//         return;
//       }

//       setCurrentHighlightedText(text);
//       addHighlight({ pageIndex, text });
//       speakFromPosition(
//         fullText,
//         selectedTextIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [
//       documentInfo,
//       speaking,
//       paused,
//       stop,
//       getPageTextAndItems,
//       speakFromPosition,
//       addHighlight,
//       speak,
//     ]
//   );

//   const handleWordClick = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

//       const wordStartIndex = textItem.startIndex;

//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         textItem.pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceClick = useCallback(
//     async (sentence: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleParagraphClick = useCallback(
//     async (paragraph: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (speaking) {
//         stop();
//       }
//       setIsAutoReading(false);
//       setIsStoppedAndReady(false);

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//         }
//       );
//     },
//     [documentInfo, speaking, stop, getPageTextAndItems, speakFromPosition]
//   );

//   const handleWordHover = useCallback(
//     async (word: string, textItem: TextItem) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       if (textItem) {
//         setHoveredReadingPosition({
//           text: word,
//           startIndex: textItem.startIndex,
//           type: "word",
//           textItem: textItem,
//         });
//       } else {
//         setHoveredReadingPosition(null);
//       }

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

//       const wordStartIndex = textItem.startIndex;

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         wordStartIndex,
//         [],
//         textItem.pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleSentenceHover = useCallback(
//     async (sentence: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleParagraphHover = useCallback(
//     async (paragraph: string, startIndex: number, pageIndex: number) => {
//       if (!documentInfo || !documentInfo.pdfDoc) return;

//       const canHover = !speaking || paused;
//       if (!canHover) return;

//       const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

//       setIsStoppedAndReady(false);
//       speakFromPosition(
//         text,
//         startIndex,
//         [],
//         pageIndex,
//         (event) => {
//           if (event.charIndex !== undefined && event.charLength !== undefined) {
//             const charIndex = event.charIndex;
//             const currentTextItem = textItems.find(
//               (item) =>
//                 charIndex >= item.startIndex && charIndex < item.endIndex
//             );
//             if (currentTextItem) {
//               setCurrentHighlightedWord(currentTextItem);
//             }
//           }
//         },
//         () => {
//           setCurrentHighlightedWord(null);
//           if (!speaking) {
//             setIsStoppedAndReady(true);
//           }
//         }
//       );
//     },
//     [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
//   );

//   const handleReadingComplete = useCallback(async () => {
//     if (!documentInfo || !isAutoReading) return;

//     const currentPage = documentInfo.currentPage;
//     const isCoverPage = currentPage === 1;
    
//     let nextPage = isCoverPage ? 2 : currentPage + 2;

//     nextPage = await findNextNonBlankPage(nextPage);

//     if (nextPage > documentInfo.totalPages) {
//       setIsAutoReading(false);
//       stop();
//       clearHighlights();
//       setCurrentHighlight(null);
//       setCurrentHighlightedWord(null);
//       setReadWords(new Set());
//       setReadPages(new Set());
//       setIsStoppedAndReady(true);
//       console.log("Document reading complete");
//     } else {
//       stop();
//       setReadWords(new Set());
//       await setCurrentPage(nextPage);
//       setIsAutoReading(true);
//       console.log(`Moving to next spread starting with page ${nextPage}`);
//     }
//   }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights, findNextNonBlankPage]);

//   useEffect(() => {
//     if (
//       !isAutoReading ||
//       !documentInfo ||
//       speaking ||
//       isLoading ||
//       !documentInfo.pdfDoc
//     )
//       return;

//     const readingTimeout = setTimeout(async () => {
//       const currentPage = documentInfo.currentPage;
//       const isCoverPage = currentPage === 1;
      
//       const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
//       const rightPage = isCoverPage ? null : leftPage + 1 <= documentInfo.totalPages ? leftPage + 1 : null;

//       console.log(`Current spread: ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

//       const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
//       if (!text || pagesRead.every((page) => readPages.has(page))) {
//         console.log("All pages in spread already read or blank, moving to next spread");
//         await handleReadingComplete();
//         return;
//       }

//       setSpreadTextItems(textItems);
//       setCurrentHighlightedText(text);

//       const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
//       const rightPageItems = rightPage ? textItems.filter(item => item.pageIndex === rightPage - 1) : [];

//       const readPage = async (pageText: string, pageItems: TextItem[], pageIndex: number) => {
//         if (!pageText) {
//           console.log(`No text on page ${pageIndex + 1}, skipping`);
//           return;
//         }
        
//         if (readPages.has(pageIndex + 1)) {
//           console.log(`Page ${pageIndex + 1} already read, skipping`);
//           return;
//         }
        
//         console.log(`Reading page ${pageIndex + 1}`);
//         await new Promise<void>((resolve) => {
//           speak(
//             pageText,
//             [],
//             pageIndex,
//             (event) => {
//               if (event.charIndex !== undefined && event.charLength !== undefined) {
//                 const charIndex = event.charIndex;
//                 const textItem = pageItems.find(
//                   (item) =>
//                     charIndex >= item.startIndex && charIndex < item.endIndex
//                 );
//                 if (textItem) {
//                   const word = pageText
//                     .substring(charIndex, charIndex + event.charLength)
//                     .trim();

//                   const wordStart = Math.max(charIndex, textItem.startIndex);
//                   const wordEnd = Math.min(
//                     charIndex + event.charLength,
//                     textItem.endIndex
//                   );

//                   const textItemLength = Math.max(
//                     textItem.endIndex - textItem.startIndex,
//                     1
//                   );
//                   const wordLength = Math.max(wordEnd - wordStart, 1);

//                   const wordProgress =
//                     (wordStart - textItem.startIndex) / textItemLength;
//                   const wordLengthRatio = wordLength / textItemLength;

//                   const clampedProgress = Math.max(0, Math.min(1, wordProgress));
//                   const clampedLengthRatio = Math.max(
//                     0.1,
//                     Math.min(1, wordLengthRatio)
//                   );

//                   const preciseX =
//                     textItem.coordinates.x +
//                     textItem.coordinates.width * clampedProgress;
//                   const preciseWidth = Math.max(
//                     textItem.coordinates.width * clampedLengthRatio,
//                     15
//                   );

//                   const highlightItem = {
//                     ...textItem,
//                     text: word,
//                     startIndex: wordStart,
//                     endIndex: wordEnd,
//                     coordinates: {
//                       ...textItem.coordinates,
//                       x: preciseX,
//                       width: preciseWidth,
//                       height: textItem.coordinates.height + 4,
//                     },
//                   };

//                   setCurrentHighlight(highlightItem);
//                   setCurrentHighlightedWord(textItem);

//                   setReadWords((prev) =>
//                     new Set(prev).add(
//                       `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
//                     )
//                   );
//                 }
//               }
//             },
//             () => {
//               setCurrentHighlight(null);
//               setCurrentHighlightedWord(null);
//               resolve();
//             }
//           );
//         });
//       };

//       try {
//         const leftText = leftPageItems.length > 0 ? (await getPageTextAndItems(leftPage)).text : "";
//         await readPage(leftText, leftPageItems, leftPage - 1);
        
//         setReadPages(prev => new Set([...prev, leftPage]));

//         if (!isCoverPage && rightPage && rightPageItems.length > 0) {
//           const rightText = (await getPageTextAndItems(rightPage)).text;
//           await readPage(rightText, rightPageItems, rightPage - 1);
          
//           setReadPages(prev => new Set([...prev, rightPage]));
//         }

//         console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
//         handleReadingComplete();
//       } catch (error) {
//         console.error("Error during auto-reading:", error);
//         handleReadingComplete();
//       }
//     }, 500);

//     return () => clearTimeout(readingTimeout);
//   }, [
//     isAutoReading,
//     documentInfo,
//     speaking,
//     isLoading,
//     getSpreadTextAndItems,
//     getPageTextAndItems,
//     speak,
//     handleReadingComplete,
//     readPages,
//   ]);

//   return (
//     <Box className="min-h-screen flex flex-col" sx={{ backgroundColor: "#9a1732" }}>
//       <Header onHistoryClick={() => {}} />
//       <Container
//         maxWidth={false}
//         sx={{
//           py: { xs: 1, sm: 2, md: 2 },
//           px: { xs: 1, sm: 1, md: 2, lg: 2, xl: 3 },
//           display: "flex",
//           flexDirection: "row",
//           height: { xs: "85vh", sm: "85vh", md: "90vh", lg: "90vh", xl: "90vh" },
//           overflow: "hidden",
//         }}
//       >
//         <Box
//           sx={{
//             display: "flex",
//             flexDirection: "column",
//             justifyContent: "space-around",
//             alignItems: "center",
//             width: { xs: "20%", sm: "20%", md: "15%", lg: "15%", xl: "15%" },
//             height: "100%",
//             p: 2,
//           }}
//         >
//           <Paper elevation={3} sx={{ p: 2, width: "100%" }}>
//             <PageTitle title="Audio Controls" sx={{ textAlign: "center" }} />
//             <Box sx={{ display: "flex", flexDirection: "column", gap: 2, mt: 2 }}>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<SkipPrevious />}
//                 onClick={async () => {
//                   if (!documentInfo) return;
//                   stop();
//                   setIsAutoReading(false);
//                   clearHighlights();
//                   setCurrentHighlight(null);
//                   setCurrentHighlightedWord(null);
//                   setIsStoppedAndReady(false);
                  
//                   const currentPage = documentInfo.currentPage;
                  
//                   let prevPage;
//                   if (currentPage <= 3) {
//                     prevPage = 1;
//                   } else {
//                     prevPage = currentPage % 2 === 0 ? currentPage - 2 : currentPage - 3;
//                   }
                  
//                   if (prevPage >= 1) {
//                     console.log(`Navigating to previous spread starting with page ${prevPage}`);
//                     await setCurrentPage(prevPage);
//                     setIsAutoReading(true);
//                   }
//                 }}
//               >
//                 Previous
//               </Button>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<PlayArrow />}
//                 onClick={async () => {
//                   if (speaking) {
//                     stop();
//                   }
                  
//                   if (!isAutoReading) {
//                     setReadPages(new Set());
//                   }
                  
//                   setIsAutoReading(true);
//                   setIsStoppedAndReady(false);
                  
//                   const currentPage = documentInfo?.currentPage || 1;
//                   const isCoverPage = currentPage === 1;
                  
//                   const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
//                   const rightPage = isCoverPage ? null : leftPage + 1 <= (documentInfo?.totalPages || 0) ? leftPage + 1 : null;

//                   console.log(`Starting to read ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

//                   const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
//                   if (!text || pagesRead.every((page) => readPages.has(page))) {
//                     console.log("All pages in spread already read or blank, moving to next spread");
//                     await handleReadingComplete();
//                     return;
//                   }

//                   setSpreadTextItems(textItems);
//                   setCurrentHighlightedText(text);

//                   const readPage = async (pageNum: number, pageItems: TextItem[]) => {
//                     if (readPages.has(pageNum)) {
//                       console.log(`Page ${pageNum} already read, skipping`);
//                       return;
//                     }
                    
//                     const pageText = (await getPageTextAndItems(pageNum)).text;
//                     if (!pageText) {
//                       console.log(`No text on page ${pageNum}, skipping`);
//                       return;
//                     }
                    
//                     console.log(`Reading page ${pageNum}`);
//                     await new Promise<void>((resolve) => {
//                       speak(
//                         pageText,
//                         [],
//                         pageNum - 1,
//                         (event) => {
//                           if (event.charIndex !== undefined && event.charLength !== undefined) {
//                             const charIndex = event.charIndex;
//                             const textItem = pageItems.find(
//                               (item) =>
//                                 charIndex >= item.startIndex && charIndex < item.endIndex
//                             );
//                             if (textItem) {
//                               setCurrentHighlightedWord(textItem);
//                               setReadWords((prev) =>
//                                 new Set(prev).add(
//                                   `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
//                                 )
//                               );
//                             }
//                           }
//                         },
//                         () => {
//                           setCurrentHighlightedWord(null);
//                           resolve();
//                         }
//                       );
//                     });
                    
//                     setReadPages(prev => new Set([...prev, pageNum]));
//                   };

//                   try {
//                     const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
//                     await readPage(leftPage, leftPageItems);
                    
//                     if (!isCoverPage && rightPage) {
//                       const rightPageItems = textItems.filter(item => item.pageIndex === rightPage - 1);
//                       await readPage(rightPage, rightPageItems);
//                     }
                    
//                     console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
//                     handleReadingComplete();
//                   } catch (error) {
//                     console.error("Error during reading:", error);
//                     handleReadingComplete();
//                   }
//                 }}
//               >
//                 Play
//               </Button>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<SkipNext />}
//                 onClick={async () => {
//                   if (!documentInfo) return;
//                   stop();
//                   setIsAutoReading(false);
//                   clearHighlights();
//                   setCurrentHighlight(null);
//                   setCurrentHighlightedWord(null);
//                   setIsStoppedAndReady(false);
                  
//                   const currentPage = documentInfo.currentPage;
//                   const isCoverPage = currentPage === 1;
                  
//                   let nextPage;
//                   if (isCoverPage) {
//                     nextPage = 2;
//                   } else {
//                     nextPage = currentPage % 2 === 0 ? currentPage + 2 : currentPage + 1;
//                   }
                  
//                   const nonBlankNextPage = await findNextNonBlankPage(nextPage);
                  
//                   if (nonBlankNextPage <= documentInfo.totalPages) {
//                     console.log(`Navigating to next spread starting with page ${nonBlankNextPage}`);
//                     await setCurrentPage(nonBlankNextPage);
//                     setIsAutoReading(true);
//                   } else {
//                     console.log("Reached end of document");
//                   }
//                 }}
//               >
//                 Next
//               </Button>
//               <Button
//                 variant="contained"
//                 fullWidth
//                 startIcon={<Stop />}
//                 onClick={() => {
//                   stop();
//                   setIsAutoReading(false);
//                   clearHighlights();
//                   setCurrentHighlight(null);
//                   setCurrentHighlightedWord(null);
//                   setIsStoppedAndReady(true);
//                   setReadPages(new Set());
//                 }}
//               >
//                 Stop
//               </Button>
//             </Box>
//           </Paper>
//         </Box>
//         <Box
//           sx={{
//             flexGrow: 1,
//             ml: { xs: "5%", sm: "5%", md: "5%", lg: "5%", xl: "10" }, // Reduced gap between audio controls and PDF
//           }}
//           ref={containerRef}
//         >
//           <PDFViewer
//             document={documentInfo}
//             highlights={highlights}
//             onPageChange={setCurrentPage}
//             onTextSelection={handleTextSelection}
//             currentHighlight={currentHighlight}
//             pageHeight={documentInfo?.pageHeight || 0}
//             isReading={isAutoReading || speaking}
//             textItems={spreadTextItems}
//             currentHighlightedWord={currentHighlightedWord}
//             onWordClick={handleWordClick}
//             onSentenceClick={handleSentenceClick}
//             onParagraphClick={handleParagraphClick}
//             onWordHover={handleWordHover}
//             onSentenceHover={handleSentenceHover}
//             onParagraphHover={handleParagraphHover}
//             speaking={speaking}
//             paused={paused}
//             readWords={readWords}
//             isCoverPage={documentInfo?.currentPage === 1}
//           />
//         </Box>
//       </Container>
//       <Footer />
//     </Box>
//   );
// };

// export default HomePage;

import React, { useState, useEffect, useCallback, useRef } from "react";
import { Container, Box, Paper, Typography, Button } from "@mui/material";
import { SkipPrevious, PlayArrow, SkipNext, Stop } from "@mui/icons-material";
import PDFViewer from "../components/PDFViewer";
import AudioControls from "../components/AudioControls";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { usePDFDocument } from "../hooks/usePDFDocument";
import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
import type { TextItem } from "../types";
import { extractTextFromPage } from "../utils/pdfHelpers";
import { PageTitle } from "../components/page-title-underline";
import { Mail, Phone, MapPin, Clock } from "lucide-react";
import type { VoiceOptions } from "../types";

const HomePage: React.FC = () => {
  const {
    documentInfo,
    highlights,
    loadDocument,
    setCurrentPage,
    addHighlight,
    clearHighlights,
    isLoading,
  } = usePDFDocument();

  const {
    voices,
    options: speechOptions,
    speaking,
    paused,
    speak,
    pause,
    resume,
    stop: stopSpeech,
    setOptions: setSpeechOptions,
    speakFromPosition,
  } = useSpeechSynthesis();

  const [currentHighlightedText, setCurrentHighlightedText] = useState("");
  const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(null);
  const [currentHighlightedWord, setCurrentHighlightedWord] = useState<TextItem | null>(null);
  const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
  const [isAutoReading, setIsAutoReading] = useState(false);
  const [isStoppedAndReady, setIsStoppedAndReady] = useState(false);
  const [readWords, setReadWords] = useState<Set<string>>(new Set());
  const [readPages, setReadPages] = useState<Set<number>>(new Set());
  const [hoveredReadingPosition, setHoveredReadingPosition] = useState<{
    text: string;
    startIndex: number;
    type: "word" | "sentence" | "paragraph";
    textItem?: TextItem;
  } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadDocument("/src/assets/sample.pdf");
  }, [loadDocument]);

  const getPageTextAndItems = useCallback(
    async (pageNumber: number) => {
      if (!documentInfo || !documentInfo.pdfDoc)
        return { text: "", textItems: [], pageIndex: pageNumber };

      const { fullText, textItems: pageTextItems } = await extractTextFromPage(
        documentInfo.pdfDoc,
        pageNumber
      );

      return { text: fullText.trim(), textItems: pageTextItems, pageIndex: pageNumber };
    },
    [documentInfo]
  );

  const getSpreadTextAndItems = useCallback(
    async (leftPage: number, rightPage: number | null) => {
      let combinedText = "";
      let combinedTextItems: TextItem[] = [];
      let pagesRead: number[] = [];

      const leftResult = await getPageTextAndItems(leftPage);
      if (leftResult.text) {
        combinedText += leftResult.text;
        combinedTextItems = [...combinedTextItems, ...leftResult.textItems.map(item => ({
          ...item,
          pageIndex: leftPage - 1
        }))];
        pagesRead.push(leftPage);
      }

      if (rightPage && rightPage <= (documentInfo?.totalPages || 0)) {
        const rightResult = await getPageTextAndItems(rightPage);
        if (rightResult.text) {
          combinedText += combinedText ? ` ${rightResult.text}` : rightResult.text;
          combinedTextItems = [...combinedTextItems, ...rightResult.textItems.map(item => ({
            ...item,
            pageIndex: rightPage - 1
          }))];
          pagesRead.push(rightPage);
        }
      }

      console.log(`Spread ${leftPage}-${rightPage}:`, { text: combinedText, textItems: combinedTextItems, pagesRead });

      return { text: combinedText, textItems: combinedTextItems, pagesRead };
    },
    [getPageTextAndItems, documentInfo]
  );

  const findNextNonBlankPage = useCallback(
    async (startPage: number): Promise<number> => {
      if (!documentInfo || !documentInfo.pdfDoc) return startPage;

      for (let page = startPage; page <= documentInfo.totalPages; page++) {
        const { text } = await getPageTextAndItems(page);
        if (text && !readPages.has(page)) {
          return page;
        }
      }
      return documentInfo.totalPages + 1;
    },
    [documentInfo, getPageTextAndItems, readPages]
  );

  const handleTextSelection = useCallback(
    async (text: string, pageIndex: number) => {
      if (!text || !documentInfo || !documentInfo.pdfDoc) return;

      const isClickable = !speaking || paused;
      if (!isClickable) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text: fullText, textItems } = await getPageTextAndItems(pageIndex + 1);

      const selectedTextIndex = fullText.indexOf(text);
      if (selectedTextIndex === -1) {
        setCurrentHighlightedText(text);
        addHighlight({ pageIndex, text });
        speak(
          text,
          [],
          pageIndex,
          (event) => {
            if (
              event.charIndex !== undefined &&
              event.charLength !== undefined
            ) {
              const charIndex = event.charIndex;
              const currentTextItem = textItems.find(
                (item) =>
                  charIndex >= item.startIndex && charIndex < item.endIndex
              );
              if (currentTextItem) {
                setCurrentHighlightedWord(currentTextItem);
              }
            }
          },
          () => {
            setCurrentHighlightedWord(null);
          }
        );
        return;
      }

      setCurrentHighlightedText(text);
      addHighlight({ pageIndex, text });
      speakFromPosition(
        fullText,
        selectedTextIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [
      documentInfo,
      speaking,
      paused,
      stopSpeech,
      getPageTextAndItems,
      speakFromPosition,
      addHighlight,
      speak,
    ]
  );

  const handleWordClick = useCallback(
    async (word: string, textItem: TextItem) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

      const wordStartIndex = textItem.startIndex;

      speakFromPosition(
        text,
        wordStartIndex,
        [],
        textItem.pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [documentInfo, speaking, stopSpeech, getPageTextAndItems, speakFromPosition]
  );

  const handleSentenceClick = useCallback(
    async (sentence: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [documentInfo, speaking, stopSpeech, getPageTextAndItems, speakFromPosition]
  );

  const handleParagraphClick = useCallback(
    async (paragraph: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [documentInfo, speaking, stopSpeech, getPageTextAndItems, speakFromPosition]
  );

  const handleWordHover = useCallback(
    async (word: string, textItem: TextItem) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      if (textItem) {
        setHoveredReadingPosition({
          text: word,
          startIndex: textItem.startIndex,
          type: "word",
          textItem: textItem,
        });
      } else {
        setHoveredReadingPosition(null);
      }

      const canHover = !speaking || paused;
      if (!canHover) return;

      const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

      const wordStartIndex = textItem.startIndex;

      setIsStoppedAndReady(false);
      speakFromPosition(
        text,
        wordStartIndex,
        [],
        textItem.pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
          if (!speaking) {
            setIsStoppedAndReady(true);
          }
        }
      );
    },
    [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
  );

  const handleSentenceHover = useCallback(
    async (sentence: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      const canHover = !speaking || paused;
      if (!canHover) return;

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      setIsStoppedAndReady(false);
      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
          if (!speaking) {
            setIsStoppedAndReady(true);
          }
        }
      );
    },
    [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
  );

  const handleParagraphHover = useCallback(
    async (paragraph: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      const canHover = !speaking || paused;
      if (!canHover) return;

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      setIsStoppedAndReady(false);
      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
          if (!speaking) {
            setIsStoppedAndReady(true);
          }
        }
      );
    },
    [documentInfo, speaking, paused, getPageTextAndItems, speakFromPosition]
  );

  const handleReadingComplete = useCallback(async () => {
    if (!documentInfo || !isAutoReading) return;

    const currentPage = documentInfo.currentPage;
    const isCoverPage = currentPage === 1;
    
    let nextPage = isCoverPage ? 2 : currentPage + 2;

    nextPage = await findNextNonBlankPage(nextPage);

    if (nextPage > documentInfo.totalPages) {
      setIsAutoReading(false);
      stopSpeech();
      clearHighlights();
      setCurrentHighlight(null);
      setCurrentHighlightedWord(null);
      setReadWords(new Set());
      setReadPages(new Set());
      setIsStoppedAndReady(true);
      console.log("Document reading complete");
    } else {
      stopSpeech();
      setReadWords(new Set());
      await setCurrentPage(nextPage);
      setIsAutoReading(true);
      console.log(`Moving to next spread starting with page ${nextPage}`);
    }
  }, [documentInfo, isAutoReading, setCurrentPage, stopSpeech, clearHighlights, findNextNonBlankPage]);

  useEffect(() => {
    if (
      !isAutoReading ||
      !documentInfo ||
      speaking ||
      isLoading ||
      !documentInfo.pdfDoc
    )
      return;

    const readingTimeout = setTimeout(async () => {
      const currentPage = documentInfo.currentPage;
      const isCoverPage = currentPage === 1;
      
      const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
      const rightPage = isCoverPage ? null : leftPage + 1 <= documentInfo.totalPages ? leftPage + 1 : null;

      console.log(`Current spread: ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

      const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
      if (!text || pagesRead.every((page) => readPages.has(page))) {
        console.log("All pages in spread already read or blank, moving to next spread");
        await handleReadingComplete();
        return;
      }

      setSpreadTextItems(textItems);
      setCurrentHighlightedText(text);

      const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
      const rightPageItems = rightPage ? textItems.filter(item => item.pageIndex === rightPage - 1) : [];

      const readPage = async (pageText: string, pageItems: TextItem[], pageIndex: number) => {
        if (!pageText) {
          console.log(`No text on page ${pageIndex + 1}, skipping`);
          return;
        }
        
        if (readPages.has(pageIndex + 1)) {
          console.log(`Page ${pageIndex + 1} already read, skipping`);
          return;
        }
        
        console.log(`Reading page ${pageIndex + 1}`);
        await new Promise<void>((resolve) => {
          speak(
            pageText,
            [],
            pageIndex,
            (event) => {
              if (event.charIndex !== undefined && event.charLength !== undefined) {
                const charIndex = event.charIndex;
                const textItem = pageItems.find(
                  (item) =>
                    charIndex >= item.startIndex && charIndex < item.endIndex
                );
                if (textItem) {
                  const word = pageText
                    .substring(charIndex, charIndex + event.charLength)
                    .trim();

                  const wordStart = Math.max(charIndex, textItem.startIndex);
                  const wordEnd = Math.min(
                    charIndex + event.charLength,
                    textItem.endIndex
                  );

                  const textItemLength = Math.max(
                    textItem.endIndex - textItem.startIndex,
                    1
                  );
                  const wordLength = Math.max(wordEnd - wordStart, 1);

                  const wordProgress =
                    (wordStart - textItem.startIndex) / textItemLength;
                  const wordLengthRatio = wordLength / textItemLength;

                  const clampedProgress = Math.max(0, Math.min(1, wordProgress));
                  const clampedLengthRatio = Math.max(
                    0.1,
                    Math.min(1, wordLengthRatio)
                  );

                  const preciseX =
                    textItem.coordinates.x +
                    textItem.coordinates.width * clampedProgress;
                  const preciseWidth = Math.max(
                    textItem.coordinates.width * clampedLengthRatio,
                    15
                  );

                  const highlightItem = {
                    ...textItem,
                    text: word,
                    startIndex: wordStart,
                    endIndex: wordEnd,
                    coordinates: {
                      ...textItem.coordinates,
                      x: preciseX,
                      width: preciseWidth,
                      height: textItem.coordinates.height + 4,
                    },
                  };

                  setCurrentHighlight(highlightItem);
                  setCurrentHighlightedWord(textItem);

                  setReadWords((prev) =>
                    new Set(prev).add(
                      `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
                    )
                  );
                }
              }
            },
            () => {
              setCurrentHighlight(null);
              setCurrentHighlightedWord(null);
              resolve();
            }
          );
        });
      };

      try {
        const leftText = leftPageItems.length > 0 ? (await getPageTextAndItems(leftPage)).text : "";
        await readPage(leftText, leftPageItems, leftPage - 1);
        
        setReadPages(prev => new Set([...prev, leftPage]));

        if (!isCoverPage && rightPage && rightPageItems.length > 0) {
          const rightText = (await getPageTextAndItems(rightPage)).text;
          await readPage(rightText, rightPageItems, rightPage - 1);
          
          setReadPages(prev => new Set([...prev, rightPage]));
        }

        console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
        handleReadingComplete();
      } catch (error) {
        console.error("Error during auto-reading:", error);
        handleReadingComplete();
      }
    }, 500);

    return () => clearTimeout(readingTimeout);
  }, [
    isAutoReading,
    documentInfo,
    speaking,
    isLoading,
    getSpreadTextAndItems,
    getPageTextAndItems,
    speak,
    handleReadingComplete,
    readPages,
  ]);

  const handlePlay = () => {
    if (speaking) {
      pause();
    } else if (!isAutoReading) {
      setReadPages(new Set());
    }
    setIsAutoReading(true);
    setIsStoppedAndReady(false);

    const currentPage = documentInfo?.currentPage || 1;
    const isCoverPage = currentPage === 1;

    const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
    const rightPage = isCoverPage ? null : leftPage + 1 <= (documentInfo?.totalPages || 0) ? leftPage + 1 : null;

    console.log(`Starting to read ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

    getSpreadTextAndItems(leftPage, rightPage).then(({ text, textItems, pagesRead }) => {
      if (!text || pagesRead.every((page) => readPages.has(page))) {
        console.log("All pages in spread already read or blank, moving to next spread");
        handleReadingComplete();
        return;
      }

      setSpreadTextItems(textItems);
      setCurrentHighlightedText(text);

      const readPage = async (pageNum: number, pageItems: TextItem[]) => {
        if (readPages.has(pageNum)) {
          console.log(`Page ${pageNum} already read, skipping`);
          return;
        }

        const pageText = (await getPageTextAndItems(pageNum)).text;
        if (!pageText) {
          console.log(`No text on page ${pageNum}, skipping`);
          return;
        }

        console.log(`Reading page ${pageNum}`);
        await new Promise<void>((resolve) => {
          speak(
            pageText,
            [],
            pageNum - 1,
            (event) => {
              if (event.charIndex !== undefined && event.charLength !== undefined) {
                const charIndex = event.charIndex;
                const textItem = pageItems.find(
                  (item) =>
                    charIndex >= item.startIndex && charIndex < item.endIndex
                );
                if (textItem) {
                  setCurrentHighlightedWord(textItem);
                  setReadWords((prev) =>
                    new Set(prev).add(
                      `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
                    )
                  );
                }
              }
            },
            () => {
              setCurrentHighlightedWord(null);
              resolve();
            }
          );
        });

        setReadPages(prev => new Set([...prev, pageNum]));
      };

      (async () => {
        try {
          const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
          await readPage(leftPage, leftPageItems);

          if (!isCoverPage && rightPage) {
            const rightPageItems = textItems.filter(item => item.pageIndex === rightPage - 1);
            await readPage(rightPage, rightPageItems);
          }

          console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
          handleReadingComplete();
        } catch (error) {
          console.error("Error during reading:", error);
          handleReadingComplete();
        }
      })();
    });
  };

  const handlePause = () => {
    pause();
  };

  const handleResume = () => {
    resume();
  };

  const handleStop = () => {
    stopSpeech();
    setIsAutoReading(false);
    clearHighlights();
    setCurrentHighlight(null);
    setCurrentHighlightedWord(null);
    setIsStoppedAndReady(true);
    setReadPages(new Set());
  };

  const handleNext = async () => {
    if (!documentInfo) return;
    stopSpeech();
    setIsAutoReading(false);
    clearHighlights();
    setCurrentHighlight(null);
    setCurrentHighlightedWord(null);
    setIsStoppedAndReady(false);

    const currentPage = documentInfo.currentPage;
    const isCoverPage = currentPage === 1;

    let nextPage;
    if (isCoverPage) {
      nextPage = 2;
    } else {
      nextPage = currentPage % 2 === 0 ? currentPage + 2 : currentPage + 1;
    }

    const nonBlankNextPage = await findNextNonBlankPage(nextPage);

    if (nonBlankNextPage <= documentInfo.totalPages) {
      console.log(`Navigating to next spread starting with page ${nonBlankNextPage}`);
      await setCurrentPage(nonBlankNextPage);
      setIsAutoReading(true);
    } else {
      console.log("Reached end of document");
    }
  };

  const handlePrevious = async () => {
    if (!documentInfo) return;
    stopSpeech();
    setIsAutoReading(false);
    clearHighlights();
    setCurrentHighlight(null);
    setCurrentHighlightedWord(null);
    setIsStoppedAndReady(false);

    const currentPage = documentInfo.currentPage;

    let prevPage;
    if (currentPage <= 3) {
      prevPage = 1;
    } else {
      prevPage = currentPage % 2 === 0 ? currentPage - 2 : currentPage - 3;
    }

    if (prevPage >= 1) {
      console.log(`Navigating to previous spread starting with page ${prevPage}`);
      await setCurrentPage(prevPage);
      setIsAutoReading(true);
    }
  };

  useEffect(() => {
    if (
      !isAutoReading ||
      !documentInfo ||
      speaking ||
      isLoading ||
      !documentInfo.pdfDoc
    )
      return;

    const readingTimeout = setTimeout(async () => {
      const currentPage = documentInfo.currentPage;
      const isCoverPage = currentPage === 1;
      
      const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
      const rightPage = isCoverPage ? null : leftPage + 1 <= documentInfo.totalPages ? leftPage + 1 : null;

      console.log(`Current spread: ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

      const { text, textItems, pagesRead } = await getSpreadTextAndItems(leftPage, rightPage);
      if (!text || pagesRead.every((page) => readPages.has(page))) {
        console.log("All pages in spread already read or blank, moving to next spread");
        await handleReadingComplete();
        return;
      }

      setSpreadTextItems(textItems);
      setCurrentHighlightedText(text);

      const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
      const rightPageItems = rightPage ? textItems.filter(item => item.pageIndex === rightPage - 1) : [];

      const readPage = async (pageText: string, pageItems: TextItem[], pageIndex: number) => {
        if (!pageText) {
          console.log(`No text on page ${pageIndex + 1}, skipping`);
          return;
        }
        
        if (readPages.has(pageIndex + 1)) {
          console.log(`Page ${pageIndex + 1} already read, skipping`);
          return;
        }
        
        console.log(`Reading page ${pageIndex + 1}`);
        await new Promise<void>((resolve) => {
          speak(
            pageText,
            [],
            pageIndex,
            (event) => {
              if (event.charIndex !== undefined && event.charLength !== undefined) {
                const charIndex = event.charIndex;
                const textItem = pageItems.find(
                  (item) =>
                    charIndex >= item.startIndex && charIndex < item.endIndex
                );
                if (textItem) {
                  const word = pageText
                    .substring(charIndex, charIndex + event.charLength)
                    .trim();

                  const wordStart = Math.max(charIndex, textItem.startIndex);
                  const wordEnd = Math.min(
                    charIndex + event.charLength,
                    textItem.endIndex
                  );

                  const textItemLength = Math.max(
                    textItem.endIndex - textItem.startIndex,
                    1
                  );
                  const wordLength = Math.max(wordEnd - wordStart, 1);

                  const wordProgress =
                    (wordStart - textItem.startIndex) / textItemLength;
                  const wordLengthRatio = wordLength / textItemLength;

                  const clampedProgress = Math.max(0, Math.min(1, wordProgress));
                  const clampedLengthRatio = Math.max(
                    0.1,
                    Math.min(1, wordLengthRatio)
                  );

                  const preciseX =
                    textItem.coordinates.x +
                    textItem.coordinates.width * clampedProgress;
                  const preciseWidth = Math.max(
                    textItem.coordinates.width * clampedLengthRatio,
                    15
                  );

                  const highlightItem = {
                    ...textItem,
                    text: word,
                    startIndex: wordStart,
                    endIndex: wordEnd,
                    coordinates: {
                      ...textItem.coordinates,
                      x: preciseX,
                      width: preciseWidth,
                      height: textItem.coordinates.height + 4,
                    },
                  };

                  setCurrentHighlight(highlightItem);
                  setCurrentHighlightedWord(textItem);

                  setReadWords((prev) =>
                    new Set(prev).add(
                      `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
                    )
                  );
                }
              }
            },
            () => {
              setCurrentHighlight(null);
              setCurrentHighlightedWord(null);
              resolve();
            }
          );
        });
      };

      try {
        const leftText = leftPageItems.length > 0 ? (await getPageTextAndItems(leftPage)).text : "";
        await readPage(leftText, leftPageItems, leftPage - 1);
        
        setReadPages(prev => new Set([...prev, leftPage]));

        if (!isCoverPage && rightPage && rightPageItems.length > 0) {
          const rightText = (await getPageTextAndItems(rightPage)).text;
          await readPage(rightText, rightPageItems, rightPage - 1);
          
          setReadPages(prev => new Set([...prev, rightPage]));
        }

        console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
        handleReadingComplete();
      } catch (error) {
        console.error("Error during auto-reading:", error);
        handleReadingComplete();
      }
    }, 500);

    return () => clearTimeout(readingTimeout);
  }, [
    isAutoReading,
    documentInfo,
    speaking,
    isLoading,
    getSpreadTextAndItems,
    getPageTextAndItems,
    speak,
    handleReadingComplete,
    readPages,
  ]);

  return (
<Box 
  sx={{ 
    minHeight: "100vh",
    height: "100vh",
    display: "flex",
    flexDirection: "column",
    backgroundColor: "white",
    overflow: "hidden",
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  }}
>
      <Header onHistoryClick={() => {}} />
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          p: { xs: 1, sm: 2 },
          height: "calc(100vh - 64px)", // Subtract header height
          position: "relative"
        }}
      >
        <Box 
          sx={{ 
            py: 1,
            px: { xs: 1, sm: 2 },
            display: "flex", 
            justifyContent: "center",
             backgroundColor: "rgba(0,0,0, 0.1)",
            width: "30%",
            borderRadius: 8,
            mb: 2,
            position: "sticky",
            top: 0,
            zIndex: 1,
            mx: "auto" 
          }}
>
          <AudioControls
            playing={speaking}
            paused={paused}
            onPlay={handlePlay}
            onPause={handlePause}
            onResume={handleResume}
            onStop={handleStop}
            onNext={handleNext}
            onPrevious={handlePrevious}
            voiceOptions={speechOptions}
            setVoiceOptions={setSpeechOptions}
            availableVoices={voices}
          />
        </Box>
        <Box
          sx={{
            flex: 1,
            position: "relative",
            backgroundColor: "rgba(255, 255, 255, 0.05)",
            borderRadius: 2,
            overflow: "auto",
            height: "calc(100% - 80px)" // Subtract audio controls height + margin
          }}
          ref={containerRef}
        >
          <PDFViewer
            document={documentInfo}
            highlights={highlights}
            onPageChange={setCurrentPage}
            onTextSelection={handleTextSelection}
            currentHighlight={currentHighlight}
            pageHeight={documentInfo?.pageHeight || 0}
            isReading={isAutoReading || speaking}
            textItems={spreadTextItems}
            currentHighlightedWord={currentHighlightedWord}
            onWordClick={handleWordClick}
            onSentenceClick={handleSentenceClick}
            onParagraphClick={handleParagraphClick}
            onWordHover={handleWordHover}
            onSentenceHover={handleSentenceHover}
            onParagraphHover={handleParagraphHover}
            speaking={speaking}
            paused={paused}
            readWords={readWords}
          />
        </Box>
      </Box>
      <Footer />
    </Box>
  );
};

export default HomePage;
