import React from "react";
import {
  Play,
  Pause,
  SkipBack as Ski<PERSON>,
  Volume2,
  Volume<PERSON>,
  <PERSON>wind,
  <PERSON>For<PERSON>,
  <PERSON><PERSON>ircle,
  Settings,
  AudioLines,
} from "lucide-react";

import {
  Box,
  IconButton,
  Slider,
  Typography,
  Paper,
  Menu,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
} from "@mui/material";
import { motion } from "framer-motion";
import type { VoiceOptions } from "../types";
import type { SelectChangeEvent } from "@mui/material/Select";
import { PageTitle } from "./dialog-title-underline";

interface AudioControlsProps {
  playing: boolean;
  paused: boolean;
  onPlay: () => void;
  onPause: () => void;
  onResume: () => void;
  onStop: () => void;
  onNext: () => void;
  onPrevious: () => void;
  voiceOptions: VoiceOptions;
  setVoiceOptions: (options: Partial<VoiceOptions>) => void;
  availableVoices: SpeechSynthesisVoice[];
}

const AudioControls: React.FC<AudioControlsProps> = ({
  playing,
  paused,
  onPlay,
  onPause,
  onResume,
  onStop,
  onNext,
  onPrevious,
  voiceOptions,
  setVoiceOptions,
  availableVoices,
}) => {
  const [settingsAnchorEl, setSettingsAnchorEl] =
    React.useState<null | HTMLElement>(null);
  const settingsOpen = Boolean(settingsAnchorEl);

  const handleSettingsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setSettingsAnchorEl(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchorEl(null);
  };

  const handleVoiceChange = (event: SelectChangeEvent<string>) => {
    const voiceURI = event.target.value;
    const selectedVoice =
      availableVoices.find((voice) => voice.voiceURI === voiceURI) || null;
    setVoiceOptions({ voice: selectedVoice });
  };

  // Animation variants for buttons
  const buttonVariants = {
    hover: { scale: 1.2, rotate: 5 },
    tap: { scale: 0.9 },
    disabled: { opacity: 0.5 },
  };

  // Animation variants for menu
  const menuVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  };

  return (
    <Box 
      sx={{ 
        display: "flex", 
        alignItems: "center", 
        justifyContent: "center", 
        gap: 2,
        backgroundColor: "#8f1d36S",
        borderRadius: 2,
        flexWrap: { xs: 'wrap', sm: 'nowrap' }, // Allow wrapping on small screens
      }}
    >
      {/* Previous Button */}
      <Tooltip title="Previous Page">
        <IconButton
          onClick={onPrevious}
          aria-label="Previous"
          component={motion.button}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          color="inherit"
          sx={{ 
            backgroundColor: "#f5f5f5",
            "&:hover": { backgroundColor: "#e0e0e0" }
          }}
        >
          <Rewind fill="black" size={16} />
        </IconButton>
      </Tooltip>

      {/* Play/Pause Button */}
      {playing && !paused ? (
        <Tooltip title="Pause">
          <IconButton
            onClick={onPause}
            aria-label="Pause"
            component={motion.button}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
            sx={{ 
              backgroundColor: "#1976d2",
              color: "white",
              "&:hover": { backgroundColor: "#1565c0" }
            }}
          >
            <Pause fill="white" size={18} />
          </IconButton>
        </Tooltip>
      ) : playing && paused ? (
        <Tooltip title="Resume">
          <IconButton
            onClick={onResume}
            aria-label="Resume"
            component={motion.button}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
            sx={{ 
              backgroundColor: "#1976d2",
              color: "white",
              "&:hover": { backgroundColor: "#1565c0" }
            }}
          >
            <Play fill="white" size={18} />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Play">
          <IconButton
            onClick={onPlay}
            aria-label="Play"
            component={motion.button}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
            sx={{ 
              backgroundColor: "#1976d2",
              color: "white",
              "&:hover": { backgroundColor: "#1565c0" }
            }}
          >
            <Play fill="white" size={20} />
          </IconButton>
        </Tooltip>
      )}

      {/* Next Button */}
      <Tooltip title="Next Page">
        <IconButton
          onClick={onNext}
          aria-label="Next"
          component={motion.button}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          color="inherit"
          sx={{ 
            backgroundColor: "#f5f5f5",
            "&:hover": { backgroundColor: "#e0e0e0" }
          }}
        >
          <FastForward fill= "black" size={16} />
        </IconButton>
      </Tooltip>

      {/* Stop Button */}
      <Tooltip title="Stop">
        <IconButton
          onClick={onStop}
          aria-label="Stop"
          disabled={!playing}
          component={motion.button}
          variants={buttonVariants}
          whileHover={playing ? "hover" : "disabled"}
          whileTap={playing ? "tap" : "disabled"}
          sx={{ 
            backgroundColor: playing ? "#d32f2f" : "#f5f5f5",
            color: playing ? "white" : "rgba(0, 0, 0, 0.26)",
            "&:hover": { 
              backgroundColor: playing ? "#c62828" : "#f5f5f5" 
            }
          }}
        >
          <StopCircle size={20} />
        </IconButton>
      </Tooltip>

      {/* Volume Controls */}
      <Box sx={{ 
        display: "flex", 
        alignItems: "center", 
        minWidth: { xs: 100, sm: 120 }, 
        ml: { xs: 0, sm: 2 },
        flexShrink: 1
      }}>
        {voiceOptions.volume === 0 ? (
          <VolumeX  size={18} className="mr-2" />
        ) : (
          <Volume2 fill="black" size={18} className="mr-2" />
        )}
        <Slider
          value={voiceOptions.volume}
          min={0}
          max={1}
          step={0.1}
          onChange={(_, value) =>
            setVoiceOptions({ volume: value as number })
          }
          aria-label="Volume"
          size="small"
          sx={{
            color: "#1C4C96",
            "& .MuiSlider-thumb": {
              transition: "all 0.2s ease",
              "&:hover": { boxShadow: "0 0 8px rgba(0,0,255,0.3)" },
            },
          }}
        />
      </Box>

      {/* Settings Button */}
      <Tooltip title="Adjust voice settings">
        <IconButton
          onClick={handleSettingsClick}
          aria-label="Voice settings"
          component={motion.button}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          sx={{ 
            backgroundColor: "#f5f5f5",
            "&:hover": { backgroundColor: "#e0e0e0" },
            ml: 1
          }}
        >
          {/* make the icon rotate clockwise */}
          <Settings size={18} />
        </IconButton>
      </Tooltip>

      {/* Settings Menu */}
      <Menu
        anchorEl={settingsAnchorEl}
        open={settingsOpen}
        onClose={handleSettingsClose}
        PaperProps={{
          style: {
            width: 250,
            padding: "8px",
            borderRadius: 8,
          },
          component: motion.div,
          variants: menuVariants,
          initial: "hidden",
          animate: "visible",
        }}
      >
        <PageTitle title="Voice Settings" />

        <FormControl fullWidth margin="dense" size="small">
          <InputLabel id="voice-select-label">Voice</InputLabel>
          <Select
            labelId="voice-select-label"
            value={voiceOptions.voice?.voiceURI || ""}
            onChange={handleVoiceChange}
            label="Voice"
            sx={{ borderRadius: 2 }}
          >
            {availableVoices.map((voice) => (
              <MenuItem key={voice.voiceURI} value={voice.voiceURI}>
                {voice.name} ({voice.lang})
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Box className="px-3 py-2">
          <Typography variant="body2" gutterBottom>
            Speed: {voiceOptions.rate.toFixed(1)}x
          </Typography>
          <Slider
            value={voiceOptions.rate}
            min={0.5}
            max={2}
            step={0.1}
            onChange={(_, value) =>
              setVoiceOptions({ rate: value as number })
            }
            aria-label="Speech rate"
            size="small"
            sx={{
              color: "#1C4C96",
              "& .MuiSlider-thumb": {
                transition: "all 0.2s ease",
                "&:hover": { boxShadow: "0 0 8px rgba(0,0,255,0.3)" },
              },
            }}
          />
        </Box>

        <Box className="px-3 py-2">
          <Typography variant="body2" gutterBottom>
            Pitch: {voiceOptions.pitch.toFixed(1)}
          </Typography>
          <Slider
            value={voiceOptions.pitch}
            min={0.5}
            max={2}
            step={0.1}
            onChange={(_, value) =>
              setVoiceOptions({ pitch: value as number })
            }
            aria-label="Speech pitch"
            size="small"
            sx={{
              color: "#1C4C96",
              "& .MuiSlider-thumb": {
                transition: "all 0.2s ease",
                "&:hover": { boxShadow: "0 0 8px rgba(0,0,255,0.3)" },
              },
            }}
          />
        </Box>
      </Menu>
    </Box>
  );
};

export default AudioControls;
